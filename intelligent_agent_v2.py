# -*- coding: utf-8 -*-
"""
智能文档处理Agent v2.0 - 重构版本

采用分析-执行两阶段架构：
1. 分析阶段：使用DocumentPlanner分析用户需求，生成结构化计划
2. 执行阶段：根据计划调用相应的WPS工具

特点：
- 模块化设计，提示词与业务逻辑分离
- 使用DeepSeek JSON输出模式确保结构化输出
- 支持流式传输和实时反馈
- 更清晰的错误处理和日志记录
"""

import json
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import os
from openai import OpenAI

# 导入新的模块
from document_planner import DocumentPlanner, StreamingDocumentPlanner
from prompts_config import PromptsConfig, DOCUMENT_TYPE_CONFIG

# 导入增强的提示词配置和规划器
from enhanced_prompts_config import EnhancedPromptsConfig, ENHANCED_DOCUMENT_TEMPLATES
from enhanced_document_planner import EnhancedDocumentPlanner, StreamingEnhancedDocumentPlanner

# 导入WPS工具
from wps_tool import (
    WPSDocumentTool, TableData, DocumentConfig, TextStyle, 
    ParagraphFormat, DocumentMetadata, WPSToolWrapper, AgentWPSInterface,
    PageStyle, HeaderFooterStyle, DocumentTheme  # 新增的样式类
)


@dataclass
class ConversationContext:
    """对话上下文管理"""
    user_id: str
    session_id: str
    messages: List[Dict[str, Any]]
    current_document: Optional[str] = None
    wps_tool_instance: Optional[WPSDocumentTool] = None
    last_operation: Optional[str] = None
    document_plan: Optional[Dict[str, Any]] = None  # 新增：存储文档计划
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class IntelligentDocumentAgentV2:
    """智能文档处理Agent v2.0
    
    采用分析-执行两阶段架构的新版本Agent
    """
    
    def __init__(self, config_path: str = "config_deepseek.json"):
        """初始化智能Agent
        
        参数:
            config_path: DeepSeek API配置文件路径
        """
        self.config = self._load_config(config_path)
        self.client = self._init_deepseek_client()
        self.conversations: Dict[str, ConversationContext] = {}
        self.logger = self._setup_logger()
        
        # 初始化增强版文档规划器
        self.planner = StreamingEnhancedDocumentPlanner(self.client)
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载DeepSeek API配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            raise Exception(f"配置文件加载失败: {e}")
    
    def _init_deepseek_client(self) -> OpenAI:
        """初始化DeepSeek客户端"""
        return OpenAI(
            api_key=self.config["deepseek_api_key"],
            base_url=self.config["base_url"]
        )
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def start_conversation(self, user_id: str, session_id: str = None) -> str:
        """开始新的对话会话"""
        if session_id is None:
            session_id = f"{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.conversations[session_id] = ConversationContext(
            user_id=user_id,
            session_id=session_id,
            messages=[]
        )
        
        self.logger.info(f"开始新对话会话: {session_id}")
        return session_id
    
    def process_user_input_stream(self, session_id: str, user_input: str, 
                                callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """流式处理用户输入 - 两阶段架构
        
        阶段1：分析用户需求，生成文档计划
        阶段2：根据计划执行具体操作
        
        参数:
            session_id: 会话ID
            user_input: 用户输入的自然语言
            callback: 回调函数，接收(message_type, content)参数
                    message_type: 'reasoning' | 'analysis' | 'content' | 'execution' | 'result'
                    
        返回:
            Dict: 包含处理结果的字典
        """
        try:
            if session_id not in self.conversations:
                return {
                    "success": False,
                    "error": "会话不存在，请先开始对话",
                    "session_id": session_id
                }
            
            context = self.conversations[session_id]
            
            # 添加用户消息到上下文
            context.messages.append({
                "role": "user",
                "content": user_input
            })
            
            if callback:
                callback('content', f"我将帮您处理文档需求：{user_input}\n")
                callback('content', "正在进行两阶段处理：\n")
                callback('content', "📋 阶段1：分析需求，制定文档计划\n")
                callback('content', "🔧 阶段2：执行计划，创建文档\n\n")
            
            # === 阶段1：需求分析和计划生成 ===
            if callback:
                callback('analysis', "=== 开始需求分析阶段 ===")
            
            analysis_result = self.planner.analyze_user_request_stream(user_input, callback)
            
            if not analysis_result.get('success'):
                return {
                    "success": False,
                    "error": f"需求分析失败: {analysis_result.get('error')}",
                    "session_id": session_id,
                    "stage": "analysis"
                }
            
            # 存储文档计划
            document_plan = analysis_result['parsed_plan']
            context.document_plan = document_plan
            
            if callback:
                callback('analysis', f"✅ 需求分析完成")
                callback('content', f"\n📋 文档计划已生成：\n")
                callback('content', f"📄 文档类型：{document_plan.get('document_type')}\n")
                callback('content', f"📝 文档标题：{document_plan.get('title')}\n")
                callback('content', f"📊 目标字数：{document_plan.get('target_words')}\n")
                callback('content', f"📑 章节数量：{len(document_plan.get('sections', []))}\n")
                callback('content', f"💾 文件名：{document_plan.get('file_name')}\n\n")
            
            # === 阶段2：执行文档创建 ===
            if callback:
                callback('execution', "=== 开始文档创建阶段 ===")
            
            execution_result = self._execute_document_plan(session_id, document_plan, callback)
            
            # 生成AI回复内容
            ai_response = self._generate_ai_response(document_plan, execution_result)
            
            # 添加助手消息到上下文
            context.messages.append({
                "role": "assistant", 
                "content": ai_response
            })
            
            return {
                "success": True,
                "session_id": session_id,
                "ai_response": ai_response,
                "document_plan": document_plan,
                "execution_result": execution_result,
                "stages_completed": ["analysis", "execution"]
            }
            
        except Exception as e:
            self.logger.error(f"流式处理用户输入时出错: {e}")
            return {
                "success": False,
                "error": f"处理请求时发生错误: {str(e)}",
                "session_id": session_id
            }
    
    def _execute_document_plan(self, session_id: str, plan: Dict[str, Any], 
                             callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """执行文档创建计划
        
        参数:
            session_id: 会话ID
            plan: 文档计划
            callback: 回调函数
            
        返回:
            Dict: 执行结果
        """
        context = self.conversations[session_id]
        execution_results = []
        
        # 确保有活动的WPS工具实例
        if not context.wps_tool_instance:
            context.wps_tool_instance = WPSDocumentTool()
            context.wps_tool_instance.connect()
            if callback:
                callback('execution', "已连接到WPS应用程序")
        
        tool = context.wps_tool_instance
        
        # 执行计划中的步骤
        execution_steps = plan.get('execution_steps', [])
        total_steps = len(execution_steps)
        
        if callback:
            callback('execution', f"开始执行 {total_steps} 个步骤...")
            callback('execution', "📋 执行计划详情:")
            for i, step in enumerate(execution_steps, 1):
                callback('execution', f"  {i}. {step.get('action')} -> {step.get('tool')}")
        
        for step in execution_steps:
            step_num = step.get('step', 0)
            action = step.get('action', '未知操作')
            tool_name = step.get('tool', '')
            parameters = step.get('parameters', {})
            
            if callback:
                callback('execution', f"步骤 {step_num}/{total_steps}: {action}")
                callback('execution', f"  🔧 工具: {tool_name}")
                callback('execution', f"  📝 参数: {parameters}")
            
            try:
                # 执行具体的工具调用
                result = self._execute_single_tool(tool, tool_name, parameters, callback)
                
                execution_results.append({
                    "step": step_num,
                    "action": action,
                    "tool": tool_name,
                    "success": result.get('success', False),
                    "result": result
                })
                
                if result.get('success'):
                    if callback:
                        callback('result', f"✅ {action} - 成功")
                else:
                    if callback:
                        callback('result', f"❌ {action} - 失败: {result.get('error', '未知错误')}")
                
            except Exception as e:
                error_msg = f"执行步骤时出错: {str(e)}"
                self.logger.error(error_msg)
                
                # 详细的错误信息（用于调试）
                import traceback
                detailed_error = traceback.format_exc()
                self.logger.error(f"详细错误信息: {detailed_error}")
                
                execution_results.append({
                    "step": step_num,
                    "action": action,
                    "tool": tool_name,
                    "success": False,
                    "error": error_msg,
                    "detailed_error": detailed_error  # 添加详细错误信息
                })
                
                if callback:
                    callback('result', f"❌ {action} - 异常: {error_msg}")
                    callback('result', f"详细错误: {detailed_error}")
        
        # 统计执行结果
        success_count = sum(1 for r in execution_results if r.get('success', False))
        
        if callback:
            callback('execution', f"文档创建完成！成功执行 {success_count}/{total_steps} 个步骤")
        
        return {
            "success": success_count > 0,
            "total_steps": total_steps,
            "success_count": success_count,
            "failed_count": total_steps - success_count,
            "step_results": execution_results
        }
    
    def _execute_single_tool(self, tool: WPSDocumentTool, tool_name: str, 
                           parameters: Dict[str, Any], 
                           callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """执行单个工具调用
        
        参数:
            tool: WPS工具实例
            tool_name: 工具名称
            parameters: 工具参数
            callback: 回调函数
            
        返回:
            Dict: 执行结果
        """
        try:
            if tool_name == "create_document":
                result = tool.create_document(
                    template_path=parameters.get("template_path")
                )
                if result["success"] and parameters.get("title"):
                    # 添加标题
                    title_style = TextStyle(font_size=18, bold=True, alignment="center")
                    tool.add_text(f"{parameters['title']}\n\n", style=title_style)
                
            elif tool_name == "add_text":
                # 处理样式参数
                style_params = parameters.get("style", {})
                style = TextStyle(
                    font_size=style_params.get("font_size", 12),
                    bold=style_params.get("bold", False),
                    italic=style_params.get("italic", False),
                    alignment=style_params.get("alignment", "left")
                )
                # 使用安全的追加方法
                result = tool.append_text_safe(
                    text=parameters.get("text", ""),
                    style=style
                )
                
            elif tool_name == "add_all_content":
                # 一次性添加所有章节内容，支持分别设置样式
                content = parameters.get("content", "")
                sections = parameters.get("sections", [])
                
                if callback:
                    callback('execution', f"收到参数 - content长度: {len(content)}, sections数量: {len(sections)}")
                
                # 如果有content参数，直接使用
                if content:
                    result = tool.append_text_safe(content)
                    if callback and result.get("success"):
                        total_chars = len(content)
                        callback('execution', f"已成功添加所有章节内容，共 {total_chars} 个字符")
                        
                # 如果没有content但有sections，从sections构建content
                elif sections:
                    if callback:
                        callback('execution', f"从 {len(sections)} 个章节构建内容...")
                    
                    # 将所有章节内容合并
                    all_content = ""
                    for section in sections:
                        section_title = section.get('title', '')
                        section_content = section.get('content', '')
                        all_content += f"\n{section_title}\n{section_content}\n\n"
                    
                    if all_content.strip():
                        result = tool.append_text_safe(all_content.strip())
                        if callback and result.get("success"):
                            total_chars = len(all_content)
                            callback('execution', f"已成功添加所有章节内容，共 {total_chars} 个字符")
                    else:
                        result = {"success": False, "error": "章节内容为空"}
                        
                else:
                    result = {"success": False, "error": "没有要添加的内容"}
                    if callback:
                        callback('execution', f"错误：既没有content参数也没有sections参数")
                
            elif tool_name == "create_table":
                table_data = TableData(
                    rows=parameters.get("rows", 3),
                    cols=parameters.get("cols", 3),
                    data=parameters.get("data", []),
                    style=parameters.get("style")
                )
                result = tool.create_table(
                    table_data=table_data,
                    caption=parameters.get("caption")
                )
                
            elif tool_name == "set_document_metadata":
                metadata = DocumentMetadata(
                    title=parameters.get("title"),
                    author=parameters.get("author"),
                    subject=parameters.get("subject"),
                    keywords=parameters.get("keywords"),
                    description=parameters.get("description"),
                    company=parameters.get("company")
                )
                result = tool.set_document_metadata(metadata)
                
            elif tool_name == "save_document":
                try:
                    # 处理文件名参数的不同格式
                    file_path = parameters.get("file_path") or parameters.get("file_name", "文档.docx")
                    
                    # 确保文件路径不与已存在的文件冲突
                    import os
                    if os.path.exists(file_path):
                        # 添加时间戳避免冲突
                        from datetime import datetime
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        name, ext = os.path.splitext(file_path)
                        file_path = f"{name}_{timestamp}{ext}"
                    
                    result = tool.save_document(
                        file_path=file_path,
                        format_type=parameters.get("format_type", "docx")
                    )
                    
                    # 保存成功后，为下次使用准备新的文档实例
                    if result.get("success"):
                        try:
                            # 断开当前文档连接，为下次创建做准备
                            tool.disconnect()
                            # 重新连接WPS应用
                            tool.connect()
                        except Exception as e:
                            # 如果重连失败，记录但不影响当前保存结果
                            if callback:
                                callback('execution', f"⚠️ 重连WPS应用时出现问题: {str(e)}")
                except Exception as e:
                    result = {
                        "success": False,
                        "error": f"保存文档时出错: {str(e)}"
                    }
                
            elif tool_name == "insert_image":
                result = tool.insert_image(
                    image_path=parameters.get("image_path"),
                    position=parameters.get("position", "end"),
                    width=parameters.get("width"),
                    height=parameters.get("height"),
                    caption=parameters.get("caption")
                )
                
            elif tool_name == "add_header_footer":
                result = tool.add_header_footer(
                    header_text=parameters.get("header_text"),
                    footer_text=parameters.get("footer_text"),
                    include_page_number=parameters.get("include_page_number", False)
                )
            
            # === 新增的样式功能工具 ===
            elif tool_name == "set_page_style":
                # 处理margins参数（可能是嵌套对象）
                margins = parameters.get("margins", {})
                if margins:
                    margin_top = margins.get("top", 2.54)
                    margin_bottom = margins.get("bottom", 2.54)
                    margin_left = margins.get("left", 3.18)
                    margin_right = margins.get("right", 3.18)
                else:
                    margin_top = parameters.get("margin_top", 2.54)
                    margin_bottom = parameters.get("margin_bottom", 2.54)
                    margin_left = parameters.get("margin_left", 3.18)
                    margin_right = parameters.get("margin_right", 3.18)
                
                page_style = PageStyle(
                    page_size=parameters.get("page_size", "A4"),
                    orientation=parameters.get("orientation", "portrait"),
                    margin_top=margin_top,
                    margin_bottom=margin_bottom,
                    margin_left=margin_left,
                    margin_right=margin_right,
                    custom_width=parameters.get("custom_width"),
                    custom_height=parameters.get("custom_height"),
                    header_distance=parameters.get("header_distance", 1.27),
                    footer_distance=parameters.get("footer_distance", 1.27),
                    gutter=parameters.get("gutter", 0.0),
                    mirror_margins=parameters.get("mirror_margins", False)
                )
                result = tool.set_page_style(page_style)
                
            elif tool_name == "set_header_footer_enhanced":
                # 创建页眉页脚样式
                header_footer_style = HeaderFooterStyle(
                    header_text=parameters.get("header_text"),
                    footer_text=parameters.get("footer_text"),
                    header_alignment=parameters.get("header_alignment", "center"),
                    footer_alignment=parameters.get("footer_alignment", "center"),
                    include_page_number=parameters.get("include_page_number", True),
                    page_number_format=parameters.get("page_number_format", "arabic"),
                    page_number_position=parameters.get("page_number_position", "footer_center"),
                    first_page_different=parameters.get("first_page_different", False),
                    odd_even_different=parameters.get("odd_even_different", False),
                    border_line=parameters.get("border_line", False)
                )
                
                # 设置页眉页脚样式
                header_style_params = parameters.get("header_style", {})
                footer_style_params = parameters.get("footer_style", {})
                
                if header_style_params:
                    header_footer_style.header_style = TextStyle(
                        font_name=header_style_params.get("font_name", "宋体"),
                        font_size=header_style_params.get("font_size", 12),
                        bold=header_style_params.get("bold", False),
                        italic=header_style_params.get("italic", False),
                        color=header_style_params.get("color")
                    )
                
                if footer_style_params:
                    header_footer_style.footer_style = TextStyle(
                        font_name=footer_style_params.get("font_name", "宋体"),
                        font_size=footer_style_params.get("font_size", 12),
                        bold=footer_style_params.get("bold", False),
                        italic=footer_style_params.get("italic", False),
                        color=footer_style_params.get("color")
                    )
                
                result = tool.set_header_footer(header_footer_style)
                
            elif tool_name == "apply_document_theme":
                # 创建文档主题
                theme = DocumentTheme(
                    theme_name=parameters.get("theme_name", "professional"),
                    primary_color=parameters.get("primary_color", "79,129,189"),
                    secondary_color=parameters.get("secondary_color", "192,192,192"),
                    accent_color=parameters.get("accent_color", "247,150,70"),
                    heading_font=parameters.get("heading_font", "微软雅黑"),
                    body_font=parameters.get("body_font", "宋体")
                )
                
                # 设置标题样式（如果提供）
                heading_styles_params = parameters.get("heading_styles", {})
                if heading_styles_params:
                    heading_styles = {}
                    for level, style_params in heading_styles_params.items():
                        heading_styles[int(level)] = TextStyle(
                            font_name=style_params.get("font_name", theme.heading_font),
                            font_size=style_params.get("font_size", 16 - int(level) * 2),
                            bold=style_params.get("bold", True),
                            color=style_params.get("color", theme.primary_color)
                        )
                    theme.heading_styles = heading_styles
                
                result = tool.apply_document_theme(theme)
                
            elif tool_name == "add_text_with_advanced_style":
                # 支持高级文本样式的添加
                text_style = TextStyle(
                    font_name=parameters.get("font_name", "宋体"),
                    font_size=parameters.get("font_size", 12),
                    bold=parameters.get("bold", False),
                    italic=parameters.get("italic", False),
                    underline=parameters.get("underline", False),
                    strikethrough=parameters.get("strikethrough", False),
                    color=parameters.get("color"),
                    highlight_color=parameters.get("highlight_color"),
                    alignment=parameters.get("alignment", "left"),
                    text_effect=parameters.get("text_effect"),
                    vertical_position=parameters.get("vertical_position", "normal"),
                    character_spacing=parameters.get("character_spacing", 0.0)
                )
                
                result = tool.add_text(
                    text=parameters.get("text", ""),
                    position=parameters.get("position", "end"),
                    style=text_style
                )
                
            elif tool_name == "add_paragraph_with_format":
                # 添加带有高级段落格式的文本
                paragraph_format = ParagraphFormat(
                    line_spacing=parameters.get("line_spacing", 1.0),
                    space_before=parameters.get("space_before", 0.0),
                    space_after=parameters.get("space_after", 0.0),
                    first_line_indent=parameters.get("first_line_indent", 0.0),
                    left_indent=parameters.get("left_indent", 0.0),
                    right_indent=parameters.get("right_indent", 0.0),
                    alignment=parameters.get("alignment", "left"),
                    border_style=parameters.get("border_style", "none"),
                    shading_color=parameters.get("shading_color"),
                    keep_with_next=parameters.get("keep_with_next", False),
                    page_break_before=parameters.get("page_break_before", False)
                )
                
                # 创建文本样式
                text_style = TextStyle(
                    font_name=parameters.get("font_name", "宋体"),
                    font_size=parameters.get("font_size", 12),
                    bold=parameters.get("bold", False),
                    italic=parameters.get("italic", False),
                    color=parameters.get("color")
                )
                
                # 先添加文本
                result = tool.add_text(
                    text=parameters.get("text", ""),
                    position=parameters.get("position", "end"),
                    style=text_style
                )
                
                # 如果需要，可以在此添加段落格式应用的逻辑
                # 注意：当前WPS工具的段落格式应用可能需要额外的实现
                
            else:
                result = {
                    "success": False,
                    "error": f"未知的工具函数: {tool_name}"
                }
                if callback:
                    callback('execution', f"❌ 错误：未知工具 '{tool_name}'")
                    callback('execution', f"📋 可用工具: create_document, add_text, add_all_content, create_table, set_document_metadata, save_document, insert_image, add_header_footer, set_page_style, set_header_footer_enhanced, apply_document_theme, add_text_with_advanced_style, add_paragraph_with_format")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": f"工具执行异常: {str(e)}"
            }
    
    def _generate_ai_response(self, plan: Dict[str, Any], execution_result: Dict[str, Any]) -> str:
        """生成AI回复内容"""
        doc_type = plan.get('document_type', '文档')
        title = plan.get('title', '文档')
        target_words = plan.get('target_words', 0)
        file_name = plan.get('file_name', '文档.docx')
        
        success_count = execution_result.get('success_count', 0)
        total_steps = execution_result.get('total_steps', 0)
        
        if success_count == total_steps:
            response = f"""✅ 文档创建成功！

📋 文档信息：
• 类型：{doc_type}
• 标题：{title}
• 字数：约{target_words}字
• 文件：{file_name}

🔧 执行情况：
• 成功完成所有 {total_steps} 个步骤
• 文档已保存到指定位置

您的{doc_type}已经创建完成，包含了详细的内容和合理的结构。如果需要进一步修改或其他帮助，请随时告诉我！"""
        else:
            failed_count = execution_result.get('failed_count', 0)
            response = f"""⚠️ 文档创建部分完成

📋 文档信息：
• 类型：{doc_type}
• 标题：{title}
• 目标字数：{target_words}字

🔧 执行情况：
• 成功步骤：{success_count}/{total_steps}
• 失败步骤：{failed_count}

文档已部分创建，但有{failed_count}个步骤执行失败。请检查错误信息或重试操作。"""
        
        return response
    
    def get_conversation_context(self, session_id: str) -> Dict[str, Any]:
        """获取对话上下文信息"""
        if session_id not in self.conversations:
            return {"error": "会话不存在"}
        
        context = self.conversations[session_id]
        return {
            "session_id": session_id,
            "user_id": context.user_id,
            "message_count": len(context.messages),
            "current_document": context.current_document,
            "last_operation": context.last_operation,
            "has_document_plan": context.document_plan is not None,
            "has_active_document": context.wps_tool_instance is not None and 
                                   context.wps_tool_instance.current_doc is not None,
            "created_at": context.created_at.isoformat()
        }
    
    def close_conversation(self, session_id: str) -> Dict[str, Any]:
        """关闭对话会话并清理资源"""
        try:
            if session_id not in self.conversations:
                return {"success": False, "error": "会话不存在"}
            
            context = self.conversations[session_id]
            
            # 清理WPS工具实例
            if context.wps_tool_instance:
                context.wps_tool_instance.disconnect()
            
            # 删除会话
            del self.conversations[session_id]
            
            self.logger.info(f"关闭对话会话: {session_id}")
            return {"success": True, "message": f"会话 {session_id} 已关闭"}
            
        except Exception as e:
            self.logger.error(f"关闭会话时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def list_active_conversations(self) -> List[Dict[str, Any]]:
        """列出所有活动的对话会话"""
        active_sessions = []
        for session_id, context in self.conversations.items():
            active_sessions.append({
                "session_id": session_id,
                "user_id": context.user_id,
                "message_count": len(context.messages),
                "current_document": context.current_document,
                "has_document_plan": context.document_plan is not None,
                "created_at": context.created_at.isoformat(),
                "has_active_document": context.wps_tool_instance is not None
            })
        return active_sessions


class SimpleAgentInterfaceV2:
    """简化的Agent接口v2.0，用于快速使用"""
    
    def __init__(self, config_path: str = "config_deepseek.json"):
        self.agent = IntelligentDocumentAgentV2(config_path)
        self.default_session_id = None
    
    def chat_stream(self, user_input: str, user_id: str = "default_user"):
        """流式聊天接口
        
        参数:
            user_input: 用户输入
            user_id: 用户ID
            
        生成器，返回(message_type, content)元组
        """
        # 如果没有活动会话，创建一个
        if not self.default_session_id:
            self.default_session_id = self.agent.start_conversation(user_id)
        
        messages_buffer = []
        
        def callback(message_type: str, content: str):
            messages_buffer.append((message_type, content))
        
        # 流式处理用户输入
        result = self.agent.process_user_input_stream(self.default_session_id, user_input, callback)
        
        # 返回所有收集到的消息
        for message_type, content in messages_buffer:
            yield message_type, content
        
        # 添加最终结果总结
        if result["success"]:
            execution_result = result.get("execution_result", {})
            success_count = execution_result.get("success_count", 0)
            total_steps = execution_result.get("total_steps", 0)
            yield "result", f"\n✅ 处理完成：成功执行 {success_count}/{total_steps} 个步骤"
        else:
            yield "result", f"\n❌ 处理失败：{result.get('error', '未知错误')}"
    
    def close(self):
        """关闭Agent并清理资源"""
        if self.default_session_id:
            self.agent.close_conversation(self.default_session_id)


# 使用示例和演示
if __name__ == "__main__":
    import sys
    
    def run_demo():
        """运行演示程序"""
        print("🚀 智能文档处理Agent v2.0 - 重构版")
        print("=" * 60)
        print("✨ 新特性：")
        print("• 分析-执行两阶段架构")
        print("• DeepSeek JSON输出模式")
        print("• 模块化设计，提示词分离")
        print("• 更准确的结构化输出")
        print("=" * 60)
        print("\n示例指令：")
        print("- 帮我创建一个1000字的软件测试文档")
        print("- 生成一份1500字的技术规范文档")
        print("- 创建用户操作手册，要求800字")
        print("-" * 60)
        
        agent = SimpleAgentInterfaceV2()
        
        while True:
            try:
                user_input = input("\n🙋 用户: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    break
                
                if not user_input:
                    continue
                
                print(f"\n🤖 AI助手正在处理...\n")
                
                # 使用流式处理
                for message_type, content in agent.chat_stream(user_input):
                    if message_type == 'reasoning':
                        print(f"🧠 {content}", end="", flush=True)
                    elif message_type == 'analysis':
                        print(f"📋 {content}")
                    elif message_type == 'content':
                        print(f"💬 {content}", end="", flush=True)
                    elif message_type == 'execution':
                        print(f"🔧 {content}")
                    elif message_type == 'result':
                        print(f"📊 {content}")
                
                print("\n" + "-" * 60)
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
        
        agent.close()
        print("\n👋 感谢使用智能文档处理Agent v2.0！")
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        run_demo()
    else:
        print("智能文档处理Agent v2.0 已加载")
        print("使用 --demo 参数运行演示程序")
        print("或者导入 SimpleAgentInterfaceV2 类进行编程使用") 