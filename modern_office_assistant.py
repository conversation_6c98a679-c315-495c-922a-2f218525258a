# -*- coding: utf-8 -*-
"""
现代化智能办公助手系统 - PyQt6界面

基于DeepSeek API的全能办公助手系统
包含文档处理、数据分析、邮件助手、日程管理等功能
采用现代化UI设计，参考豆包AI助手风格
"""

import sys
import json
import os
import threading
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTextEdit, QLineEdit, QPushButton, QLabel, QSplitter, QFrame,
    QComboBox, QCheckBox, QProgressBar, QTabWidget, QGroupBox,
    QScrollArea, QListWidget, QListWidgetItem, QMessageBox,
    QFileDialog, QStatusBar, QToolBar, QSpacerItem, QSizePolicy,
    QGridLayout, QStackedWidget, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QPropertyAnimation, 
    QEasingCurve, QRect, pyqtSlot, QParallelAnimationGroup
)
from PyQt6.QtGui import (
    QFont, QIcon, QPixmap, QPalette, QColor, QAction,
    QTextCharFormat, QTextCursor, QSyntaxHighlighter, QTextDocument,
    QLinearGradient, QPainter, QPen, QBrush, QFontMetrics
)

# 导入项目模块
from intelligent_agent_v2 import IntelligentDocumentAgentV2
from enhanced_prompts_config import EnhancedPromptsConfig
from ui_design_system import ModernDesignSystem, ModernStyleSheets
from enhanced_ui_components import (
    EnhancedModernCard, EnhancedChatMessage, EnhancedNavigationButton,
    EnhancedProgressIndicator, EnhancedStatusBar
)


class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title: str, icon: str = "", description: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.description = description
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(200, 120)
        self.setStyleSheet("""
            ModernCard {
                background-color: white;
                border: 1px solid #E5E5E5;
                border-radius: 12px;
                padding: 16px;
            }
            ModernCard:hover {
                border-color: #4F46E5;
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
            }
        """)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 图标和标题
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setFont(QFont("Segoe UI Emoji", 20))
        icon_label.setFixedSize(32, 32)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # 描述
        if self.description:
            desc_label = QLabel(self.description)
            desc_label.setFont(QFont("Microsoft YaHei", 9))
            desc_label.setStyleSheet("color: #6B7280;")
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label)
        
        layout.addStretch()
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 添加点击动画效果
            self.setStyleSheet("""
                ModernCard {
                    background-color: #F3F4F6;
                    border: 2px solid #4F46E5;
                    border-radius: 12px;
                    padding: 16px;
                    transform: scale(0.98);
                }
            """)
            
            # 恢复正常样式
            QTimer.singleShot(100, self.restore_style)
            
            # 寻找最近的 ModernOfficeAssistant 父窗口
            parent = self.parent()
            while parent and not isinstance(parent, ModernOfficeAssistant):
                parent = parent.parent()
            
            if parent and hasattr(parent, 'on_card_clicked'):
                parent.on_card_clicked(self.title)
    
    def restore_style(self):
        """恢复正常样式"""
        self.setStyleSheet("""
            ModernCard {
                background-color: white;
                border: 1px solid #E5E5E5;
                border-radius: 12px;
                padding: 16px;
            }
            ModernCard:hover {
                border-color: #4F46E5;
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
            }
        """)


class ChatMessage(QFrame):
    """现代化聊天消息组件"""
    
    def __init__(self, message: str, is_user: bool = True, message_type: str = "text"):
        super().__init__()
        self.message = message
        self.is_user = is_user
        self.message_type = message_type
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        if self.is_user:
            # 用户消息 - 右对齐
            layout.addStretch()
            
            message_frame = QFrame()
            message_frame.setMaximumWidth(500)
            message_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4F46E5, stop:1 #7C3AED);
                    border-radius: 18px;
                    padding: 12px 16px;
                    margin: 4px;
                }
            """)
            
            message_layout = QVBoxLayout()
            message_frame.setLayout(message_layout)
            
            text_label = QLabel(self.message)
            text_label.setWordWrap(True)
            text_label.setStyleSheet("color: white; font-size: 14px;")
            text_label.setFont(QFont("Microsoft YaHei", 10))
            message_layout.addWidget(text_label)
            
            layout.addWidget(message_frame)
            
        else:
            # AI消息 - 左对齐
            # AI头像
            avatar_label = QLabel("🤖")
            avatar_label.setFont(QFont("Segoe UI Emoji", 24))
            avatar_label.setFixedSize(40, 40)
            avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            avatar_label.setStyleSheet("""
                background-color: #F3F4F6;
                border-radius: 20px;
                margin: 4px;
            """)
            layout.addWidget(avatar_label)
            
            message_frame = QFrame()
            message_frame.setMaximumWidth(500)
            message_frame.setStyleSheet("""
                QFrame {
                    background-color: #F9FAFB;
                    border: 1px solid #E5E7EB;
                    border-radius: 18px;
                    padding: 12px 16px;
                    margin: 4px;
                }
            """)
            
            message_layout = QVBoxLayout()
            message_frame.setLayout(message_layout)
            
            text_label = QLabel(self.message)
            text_label.setWordWrap(True)
            text_label.setStyleSheet("color: #1F2937; font-size: 14px;")
            text_label.setFont(QFont("Microsoft YaHei", 10))
            message_layout.addWidget(text_label)
            
            layout.addWidget(message_frame)
            layout.addStretch()
        
        # 时间戳
        time_label = QLabel(datetime.now().strftime("%H:%M"))
        time_label.setFont(QFont("Microsoft YaHei", 8))
        time_label.setStyleSheet("color: #9CA3AF; margin: 4px;")
        
        if self.is_user:
            layout.addWidget(time_label)
        else:
            layout.insertWidget(1, time_label)


class ModernOfficeAssistant(QMainWindow):
    """现代化智能办公助手主界面"""
    
    def __init__(self):
        super().__init__()
        self.agent = None
        self.current_session = None
        self.worker = None
        self.current_function = "chat"
        self.setup_ui()
        self.setup_connections()
        self.initialize_agent()
    
    def setup_ui(self):
        """设置现代化用户界面"""
        self.setWindowTitle("智能办公助手 - AI Office Assistant")
        self.setGeometry(100, 100, 1400, 900)

        # 设置应用样式 - 使用新的设计系统
        combined_styles = (
            ModernStyleSheets.get_main_window_style() +
            ModernStyleSheets.get_button_style() +
            ModernStyleSheets.get_input_style() +
            ModernStyleSheets.get_scrollbar_style() +
            ModernStyleSheets.get_group_box_style() +
            ModernStyleSheets.get_combo_box_style()
        )
        self.setStyleSheet(combined_styles)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 左侧导航栏
        left_nav = self.create_navigation_panel()
        main_layout.addWidget(left_nav)
        
        # 右侧主内容区
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)
        
        # 创建各个功能页面
        self.create_function_pages()
        
        # 设置比例
        main_layout.setStretch(0, 0)  # 导航栏固定宽度
        main_layout.setStretch(1, 1)  # 内容区占满剩余空间
    
    def create_navigation_panel(self) -> QWidget:
        """创建现代化导航面板"""
        nav_panel = QWidget()
        nav_panel.setFixedWidth(300)  # 稍微增加宽度
        nav_panel.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border-right: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
            }}
        """)
        
        layout = QVBoxLayout()
        nav_panel.setLayout(layout)
        
        # 顶部品牌区域
        brand_frame = QFrame()
        brand_frame.setFixedHeight(90)  # 稍微增加高度
        brand_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('primary.main')},
                    stop:1 {ModernDesignSystem.get_color('primary.light')});
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                margin: {ModernDesignSystem.get_spacing('lg')}px;
            }}
        """)
        
        brand_layout = QHBoxLayout()
        brand_frame.setLayout(brand_layout)

        logo_label = QLabel("🚀")
        logo_label.setFont(ModernDesignSystem.get_font('2xl', 'normal'))
        logo_label.setStyleSheet("color: white;")
        brand_layout.addWidget(logo_label)

        brand_text = QLabel("智能办公助手")
        brand_text.setFont(ModernDesignSystem.get_font('lg', 'bold'))
        brand_text.setStyleSheet("color: white;")
        brand_layout.addWidget(brand_text)

        brand_layout.addStretch()
        layout.addWidget(brand_frame)
        
        # 导航按钮组
        self.nav_buttons = {}
        nav_items = [
            ("chat", "💬", "智能对话", "与AI助手自由对话"),
            ("document", "📄", "文档助手", "创建各种专业文档"),
            ("data", "📊", "数据分析", "数据处理和可视化"),
            ("email", "📧", "邮件助手", "邮件撰写和管理"),
            ("schedule", "📅", "日程管理", "智能日程规划"),
            ("translate", "🌐", "翻译工具", "多语言翻译服务"),
            ("summary", "📋", "内容总结", "文档和网页总结"),
            ("code", "💻", "代码助手", "编程辅助和代码生成")
        ]
        
        for func_id, icon, title, desc in nav_items:
            btn = EnhancedNavigationButton(func_id, icon, title, desc)
            btn.clicked.connect(lambda checked, fid=func_id: self.switch_page(fid))
            self.nav_buttons[func_id] = btn
            layout.addWidget(btn)
        
        # 默认选中对话
        self.nav_buttons["chat"].setChecked(True)
        
        layout.addStretch()
        
        # 底部设置区域
        settings_frame = QFrame()
        settings_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_50')};
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px;
                margin: {ModernDesignSystem.get_spacing('lg')}px;
            }}
        """)
        
        settings_layout = QVBoxLayout()
        settings_frame.setLayout(settings_layout)
        
        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                text-align: left;
                padding: {ModernDesignSystem.get_spacing('sm')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_600')};
                border-radius: {ModernDesignSystem.get_border_radius('sm')}px;
            }}
            QPushButton:hover {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_200')};
            }}
        """)
        settings_layout.addWidget(settings_btn)
        
        help_btn = QPushButton("❓ 帮助")
        help_btn.setStyleSheet(settings_btn.styleSheet())
        settings_layout.addWidget(help_btn)
        
        layout.addWidget(settings_frame)
        
        return nav_panel
    
    def create_nav_button(self, func_id: str, icon: str, title: str, desc: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton()
        btn.setCheckable(True)
        btn.setFixedHeight(60)
        btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                text-align: left;
                padding: 12px 16px;
                margin: 2px 16px;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #EEF2FF, stop:1 #E0E7FF);
                border-left: 3px solid #4F46E5;
            }
        """)
        
        # 创建按钮内容
        btn_layout = QHBoxLayout()
        btn.setLayout(btn_layout)
        
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI Emoji", 16))
        btn_layout.addWidget(icon_label)
        
        text_layout = QVBoxLayout()
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937;")
        text_layout.addWidget(title_label)
        
        desc_label = QLabel(desc)
        desc_label.setFont(QFont("Microsoft YaHei", 9))
        desc_label.setStyleSheet("color: #6B7280;")
        text_layout.addWidget(desc_label)
        
        btn_layout.addLayout(text_layout)
        btn_layout.addStretch()
        
        # 绑定点击事件
        btn.clicked.connect(lambda: self.switch_function(func_id))
        
        return btn
    
    def create_function_pages(self):
        """创建各功能页面"""
        # 智能对话页面
        self.chat_page = self.create_chat_page()
        self.content_stack.addWidget(self.chat_page)
        
        # 文档助手页面
        self.document_page = self.create_document_page()
        self.content_stack.addWidget(self.document_page)
        
        # 数据分析页面
        self.data_page = self.create_data_page()
        self.content_stack.addWidget(self.data_page)
        
        # 邮件助手页面
        self.email_page = self.create_email_page()
        self.content_stack.addWidget(self.email_page)
        
        # 日程管理页面
        self.schedule_page = self.create_schedule_page()
        self.content_stack.addWidget(self.schedule_page)
        
        # 翻译工具页面
        self.translate_page = self.create_translate_page()
        self.content_stack.addWidget(self.translate_page)
        
        # 内容总结页面
        self.summary_page = self.create_summary_page()
        self.content_stack.addWidget(self.summary_page)
        
        # 代码助手页面
        self.code_page = self.create_code_page()
        self.content_stack.addWidget(self.code_page)
    
    def create_chat_page(self) -> QWidget:
        """创建智能对话页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        # 页面标题
        title_frame = QFrame()
        title_frame.setFixedHeight(60)
        title_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        
        title_layout = QHBoxLayout()
        title_frame.setLayout(title_layout)
        
        title_label = QLabel("💬 智能对话")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 新建对话按钮
        new_chat_btn = QPushButton("+ 新建对话")
        new_chat_btn.setStyleSheet("""
            QPushButton {
                background: #4F46E5;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                margin: 16px;
            }
            QPushButton:hover {
                background: #4338CA;
            }
        """)
        new_chat_btn.clicked.connect(self.new_chat)
        title_layout.addWidget(new_chat_btn)
        
        layout.addWidget(title_frame)
        
        # 聊天区域
        self.chat_scroll = QScrollArea()
        self.chat_scroll.setWidgetResizable(True)
        self.chat_scroll.setStyleSheet("background: white;")
        
        self.chat_content = QWidget()
        self.chat_layout = QVBoxLayout()
        self.chat_content.setLayout(self.chat_layout)
        self.chat_scroll.setWidget(self.chat_content)
        
        layout.addWidget(self.chat_scroll)
        
        # 输入区域
        input_frame = QFrame()
        input_frame.setFixedHeight(100)
        input_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-top: 1px solid #E5E7EB;
            }
        """)
        
        input_layout = QHBoxLayout()
        input_frame.setLayout(input_layout)
        
        self.chat_input = QLineEdit()
        self.chat_input.setPlaceholderText("请输入您的问题或需求...")
        self.chat_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E5E7EB;
                border-radius: 24px;
                padding: 12px 20px;
                font-size: 14px;
                margin: 16px;
            }
            QLineEdit:focus {
                border-color: #4F46E5;
            }
        """)
        input_layout.addWidget(self.chat_input)
        
        self.send_btn = QPushButton("发送")
        self.send_btn.setFixedSize(80, 48)
        self.send_btn.setStyleSheet("""
            QPushButton {
                background: #4F46E5;
                color: white;
                border: none;
                border-radius: 24px;
                font-weight: bold;
                margin: 16px;
            }
            QPushButton:hover {
                background: #4338CA;
            }
        """)
        self.send_btn.clicked.connect(self.send_chat_message)
        input_layout.addWidget(self.send_btn)
        
        layout.addWidget(input_frame)
        
        # 添加欢迎消息
        self.add_welcome_message()
        
        return page
    
    def create_document_page(self) -> QWidget:
        """创建文档助手页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        # 页面标题
        title_label = QLabel("📄 文档助手")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 快速功能卡片
        cards_frame = QFrame()
        cards_layout = QGridLayout()
        cards_frame.setLayout(cards_layout)
        
        doc_cards = [
            ("📊 商务报告", "创建专业的商务报告和分析文档"),
            ("📚 技术文档", "生成详细的技术文档和API文档"),
            ("🎓 学术论文", "撰写规范的学术论文和研究报告"),
            ("📖 用户手册", "制作清晰的产品使用手册"),
            ("📋 项目计划", "制定详细的项目计划和时间表"),
            ("📝 会议纪要", "整理和格式化会议记录"),
        ]
        
        for i, (title, desc) in enumerate(doc_cards):
            card = EnhancedModernCard(title, "", desc, "开始创建")
            card.clicked.connect(lambda t=title: self.on_card_clicked(t))
            cards_layout.addWidget(card, i // 3, i % 3)
        
        layout.addWidget(cards_frame)
        layout.addStretch()
        
        return page
    
    def create_data_page(self) -> QWidget:
        """创建数据分析页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("📊 数据分析")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 功能说明
        info_label = QLabel("上传数据文件，AI将帮您进行数据分析和可视化")
        info_label.setStyleSheet("color: #6B7280; padding: 0 16px;")
        layout.addWidget(info_label)
        
        # 上传区域
        upload_frame = QFrame()
        upload_frame.setFixedHeight(150)
        upload_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px dashed #CBD5E1;
                border-radius: 12px;
                margin: 16px;
            }
        """)
        
        upload_layout = QVBoxLayout()
        upload_frame.setLayout(upload_layout)
        
        upload_icon = QLabel("📁")
        upload_icon.setFont(QFont("Segoe UI Emoji", 32))
        upload_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        upload_layout.addWidget(upload_icon)
        
        upload_text = QLabel("拖拽文件到此处或点击上传")
        upload_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        upload_text.setStyleSheet("color: #6B7280;")
        upload_layout.addWidget(upload_text)
        
        layout.addWidget(upload_frame)
        layout.addStretch()
        
        return page
    
    def create_email_page(self) -> QWidget:
        """创建邮件助手页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("📧 邮件助手")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 邮件模板
        templates_frame = QFrame()
        templates_layout = QGridLayout()
        templates_frame.setLayout(templates_layout)
        
        email_templates = [
            ("💼 商务邮件", "专业的商务沟通邮件模板"),
            ("📋 会议邀请", "会议邀请和日程安排邮件"),
            ("📢 通知公告", "内部通知和公告邮件"),
            ("🎉 祝贺邮件", "节日祝福和庆祝邮件"),
        ]
        
        for i, (title, desc) in enumerate(email_templates):
            card = ModernCard(title, "", desc)
            templates_layout.addWidget(card, i // 2, i % 2)
        
        layout.addWidget(templates_frame)
        layout.addStretch()
        
        return page
    
    def create_schedule_page(self) -> QWidget:
        """创建日程管理页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("📅 日程管理")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 日程功能
        schedule_frame = QFrame()
        schedule_layout = QGridLayout()
        schedule_frame.setLayout(schedule_layout)
        
        schedule_features = [
            ("📝 创建日程", "智能创建和管理日程安排"),
            ("⏰ 提醒设置", "设置智能提醒和通知"),
            ("📊 时间分析", "分析时间使用情况"),
            ("🔄 同步日历", "与其他日历应用同步"),
        ]
        
        for i, (title, desc) in enumerate(schedule_features):
            card = ModernCard(title, "", desc)
            schedule_layout.addWidget(card, i // 2, i % 2)
        
        layout.addWidget(schedule_frame)
        layout.addStretch()
        
        return page
    
    def create_translate_page(self) -> QWidget:
        """创建翻译工具页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("🌐 翻译工具")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 翻译界面
        translate_frame = QFrame()
        translate_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 12px;
                margin: 16px;
                padding: 16px;
            }
        """)
        
        translate_layout = QVBoxLayout()
        translate_frame.setLayout(translate_layout)
        
        # 语言选择
        lang_layout = QHBoxLayout()
        
        from_lang = QComboBox()
        from_lang.addItems(["中文", "英语", "日语", "韩语", "法语", "德语", "西班牙语"])
        from_lang.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background: white;
            }
        """)
        lang_layout.addWidget(from_lang)
        
        swap_btn = QPushButton("⇄")
        swap_btn.setFixedSize(40, 40)
        swap_btn.setStyleSheet("""
            QPushButton {
                background: #F3F4F6;
                border: none;
                border-radius: 20px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: #E5E7EB;
            }
        """)
        lang_layout.addWidget(swap_btn)
        
        to_lang = QComboBox()
        to_lang.addItems(["英语", "中文", "日语", "韩语", "法语", "德语", "西班牙语"])
        to_lang.setStyleSheet(from_lang.styleSheet())
        lang_layout.addWidget(to_lang)
        
        translate_layout.addLayout(lang_layout)
        
        # 输入输出区域
        io_layout = QHBoxLayout()
        
        input_text = QTextEdit()
        input_text.setPlaceholderText("请输入要翻译的文本...")
        input_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        io_layout.addWidget(input_text)
        
        output_text = QTextEdit()
        output_text.setPlaceholderText("翻译结果将显示在这里...")
        output_text.setStyleSheet(input_text.styleSheet())
        output_text.setReadOnly(True)
        io_layout.addWidget(output_text)
        
        translate_layout.addLayout(io_layout)
        
        # 翻译按钮
        translate_btn = QPushButton("翻译")
        translate_btn.setStyleSheet("""
            QPushButton {
                background: #4F46E5;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #4338CA;
            }
        """)
        translate_layout.addWidget(translate_btn)
        
        layout.addWidget(translate_frame)
        layout.addStretch()
        
        return page
    
    def create_summary_page(self) -> QWidget:
        """创建内容总结页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("📋 内容总结")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 总结类型
        summary_frame = QFrame()
        summary_layout = QGridLayout()
        summary_frame.setLayout(summary_layout)
        
        summary_types = [
            ("📄 文档总结", "总结长文档的关键信息"),
            ("🌐 网页总结", "提取网页核心内容"),
            ("🎥 视频总结", "生成视频内容摘要"),
            ("📊 数据总结", "分析和总结数据报告"),
        ]
        
        for i, (title, desc) in enumerate(summary_types):
            card = ModernCard(title, "", desc)
            summary_layout.addWidget(card, i // 2, i % 2)
        
        layout.addWidget(summary_frame)
        layout.addStretch()
        
        return page
    
    def create_code_page(self) -> QWidget:
        """创建代码助手页面"""
        page = QWidget()
        layout = QVBoxLayout()
        page.setLayout(layout)
        
        title_label = QLabel("💻 代码助手")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1F2937; padding: 16px;")
        layout.addWidget(title_label)
        
        # 代码功能
        code_frame = QFrame()
        code_layout = QGridLayout()
        code_frame.setLayout(code_layout)
        
        code_features = [
            ("🔧 代码生成", "根据需求生成代码"),
            ("🐛 代码调试", "帮助查找和修复bug"),
            ("📝 代码注释", "为代码添加详细注释"),
            ("🔄 代码重构", "优化和重构代码结构"),
            ("📚 代码解释", "解释复杂代码逻辑"),
            ("🚀 性能优化", "提供性能优化建议"),
        ]
        
        for i, (title, desc) in enumerate(code_features):
            card = ModernCard(title, "", desc)
            code_layout.addWidget(card, i // 3, i % 3)
        
        layout.addWidget(code_frame)
        layout.addStretch()
        
        return page
    
    def setup_connections(self):
        """设置信号连接"""
        self.chat_input.returnPressed.connect(self.send_chat_message)
    
    def initialize_agent(self):
        """初始化AI Agent"""
        try:
            if not os.path.exists("config_deepseek.json"):
                self.show_error("配置文件缺失", "找不到 config_deepseek.json 配置文件！")
                return
            
            self.agent = IntelligentDocumentAgentV2()
            self.current_session = self.agent.start_conversation("office_user")
            
            # 更新状态
            self.statusBar().showMessage("✅ 系统已就绪")
            
        except Exception as e:
            self.show_error("初始化错误", f"初始化AI Agent失败：\n{str(e)}")
    
    def switch_function(self, func_id: str):
        """切换功能"""
        # 更新导航按钮状态
        for btn_id, btn in self.nav_buttons.items():
            btn.setChecked(btn_id == func_id)
        
        # 切换页面
        page_map = {
            "chat": 0,
            "document": 1,
            "data": 2,
            "email": 3,
            "schedule": 4,
            "translate": 5,
            "summary": 6,
            "code": 7
        }
        
        if func_id in page_map:
            self.content_stack.setCurrentIndex(page_map[func_id])
            self.current_function = func_id
    
    def add_welcome_message(self):
        """添加欢迎消息"""
        welcome_msg = """🎉 欢迎使用智能办公助手！

我是您的AI办公伙伴，可以协助您完成各种办公任务：

📄 **文档处理** - 创建报告、论文、手册等专业文档
📊 **数据分析** - 分析数据、生成图表和报告
📧 **邮件助手** - 撰写各类商务邮件
📅 **日程管理** - 智能规划和管理日程
🌐 **翻译工具** - 多语言翻译服务
📋 **内容总结** - 快速总结长文档和网页
💻 **代码助手** - 编程辅助和代码生成

请告诉我您需要什么帮助，我会尽力为您提供最佳的解决方案！✨"""
        
        message = EnhancedChatMessage(welcome_msg, is_user=False, timestamp=datetime.now().strftime("%H:%M"))
        self.chat_layout.addWidget(message)
        self.chat_layout.addStretch()
    
    def new_chat(self):
        """新建对话"""
        if self.agent:
            self.current_session = self.agent.start_conversation("office_user")
            
            # 清空聊天记录
            for i in reversed(range(self.chat_layout.count())):
                item = self.chat_layout.itemAt(i)
                if item.widget():
                    item.widget().setParent(None)
            
            # 添加欢迎消息
            self.add_welcome_message()
            
            self.statusBar().showMessage("🆕 新对话已创建")
    
    def send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.text().strip()
        if not message:
            return
        
        if not self.agent:
            self.show_error("系统未就绪", "AI Agent未初始化，请重启程序！")
            return
        
        # 清空输入框
        self.chat_input.clear()
        
        # 添加用户消息
        user_msg = EnhancedChatMessage(message, is_user=True, timestamp=datetime.now().strftime("%H:%M"))
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, user_msg)
        
        # 滚动到底部
        QTimer.singleShot(50, self.scroll_to_bottom)
        
        # 模拟AI回复（这里应该调用实际的AI处理）
        QTimer.singleShot(1000, lambda: self.simulate_ai_response(message))
    
    def simulate_ai_response(self, user_message: str):
        """模拟AI回复"""
        ai_response = f"您说：{user_message}\n\n我正在为您处理这个请求，请稍等..."
        
        ai_msg = EnhancedChatMessage(ai_response, is_user=False, timestamp=datetime.now().strftime("%H:%M"))
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, ai_msg)
        
        # 滚动到底部
        QTimer.singleShot(50, self.scroll_to_bottom)
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.chat_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def on_card_clicked(self, card_title: str):
        """处理卡片点击事件"""
        print(f"点击了卡片：{card_title}")
        
        # 先切换到聊天页面
        self.switch_function("chat")
        
        # 根据卡片类型执行相应操作
        if "商务报告" in card_title:
            self.handle_business_report()
        elif "技术文档" in card_title:
            self.handle_technical_doc()
        elif "学术论文" in card_title:
            self.handle_academic_paper()
        elif "用户手册" in card_title:
            self.handle_user_manual()
        elif "项目计划" in card_title:
            self.handle_project_plan()
        elif "会议纪要" in card_title:
            self.handle_meeting_minutes()
        else:
            # 通用处理
            self.handle_generic_task(card_title)
    
    def handle_business_report(self):
        """处理商务报告"""
        self.statusBar().showMessage("📊 正在准备商务报告模板...")
        # 如果有agent，可以调用相应的方法
        if self.agent:
            prompt = "请帮我创建一个商务报告模板，包含执行摘要、市场分析、财务数据等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_technical_doc(self):
        """处理技术文档"""
        self.statusBar().showMessage("📚 正在准备技术文档模板...")
        if self.agent:
            prompt = "请帮我创建一个技术文档模板，包含API文档、架构说明、使用指南等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_academic_paper(self):
        """处理学术论文"""
        self.statusBar().showMessage("🎓 正在准备学术论文模板...")
        if self.agent:
            prompt = "请帮我创建一个学术论文模板，包含摘要、引言、方法、结果、讨论等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_user_manual(self):
        """处理用户手册"""
        self.statusBar().showMessage("📖 正在准备用户手册模板...")
        if self.agent:
            prompt = "请帮我创建一个用户手册模板，包含产品介绍、使用说明、故障排除等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_project_plan(self):
        """处理项目计划"""
        self.statusBar().showMessage("📋 正在准备项目计划模板...")
        if self.agent:
            prompt = "请帮我创建一个项目计划模板，包含项目概述、时间表、资源分配、风险评估等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_meeting_minutes(self):
        """处理会议纪要"""
        self.statusBar().showMessage("📝 正在准备会议纪要模板...")
        if self.agent:
            prompt = "请帮我创建一个会议纪要模板，包含会议信息、讨论内容、决议事项、行动计划等部分。"
            self.simulate_ai_response(prompt)
    
    def handle_generic_task(self, task_name: str):
        """处理通用任务"""
        self.statusBar().showMessage(f"🔧 正在处理：{task_name}")
        if self.agent:
            prompt = f"请帮我处理以下任务：{task_name}"
            self.simulate_ai_response(prompt)
    
    def show_error(self, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
        self.statusBar().showMessage(f"❌ {title}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用属性
    app.setApplicationName("智能办公助手")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Office Assistant")
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = ModernOfficeAssistant()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 