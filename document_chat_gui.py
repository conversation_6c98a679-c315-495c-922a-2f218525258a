# -*- coding: utf-8 -*-
"""
智能文档处理系统 - PyQt6对话界面

基于DeepSeek API的智能文档创建和处理系统的图形界面
支持多种文档类型、主题选择和实时对话功能
"""

import sys
import json
import os
import threading
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTextEdit, QLineEdit, QPushButton, QLabel, QSplitter, QFrame,
    QComboBox, QCheckBox, QProgressBar, QTabWidget, QGroupBox,
    QScrollArea, QListWidget, QListWidgetItem, QMessageBox,
    QFileDialog, QStatusBar, QToolBar, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QPropertyAnimation, 
    QEasingCurve, QRect, pyqtSlot
)
from PyQt6.QtGui import (
    QFont, QIcon, QPixmap, QPalette, QColor, QAction,
    QTextCharFormat, QTextCursor, QSyntaxHighlighter, QTextDocument
)

# 导入项目模块
from intelligent_agent_v2 import IntelligentDocumentAgentV2
from enhanced_prompts_config import EnhancedPromptsConfig
from ui_design_system import ModernDesignSystem, ModernStyleSheets
from enhanced_ui_components import (
    EnhancedModernCard, EnhancedChatMessage, EnhancedProgressIndicator,
    EnhancedStatusBar
)


class DocumentWorker(QThread):
    """文档处理工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(str, str)  # 阶段, 消息
    document_completed = pyqtSignal(dict)     # 完成结果
    error_occurred = pyqtSignal(str)          # 错误信息
    
    def __init__(self, agent: IntelligentDocumentAgentV2, user_input: str, session_id: str):
        super().__init__()
        self.agent = agent
        self.user_input = user_input
        self.session_id = session_id
        self.is_running = True
    
    def run(self):
        """执行文档处理任务"""
        try:
            # 使用流式处理 - 修复：正确的参数顺序
            result = self.agent.process_user_input_stream(
                self.session_id,        # 第一个参数：session_id
                self.user_input,        # 第二个参数：user_input
                callback=self.progress_callback
            )
            
            if result.get('success'):
                self.document_completed.emit(result)
            else:
                self.error_occurred.emit(result.get('error', '未知错误'))
                
        except Exception as e:
            self.error_occurred.emit(f"处理过程中出现错误: {str(e)}")
    
    def progress_callback(self, stage: str, message: str):
        """进度回调函数"""
        if self.is_running:
            self.progress_updated.emit(stage, message)
    
    def stop(self):
        """停止处理"""
        self.is_running = False
        self.quit()


class ChatBubble(QFrame):
    """聊天气泡组件"""
    
    def __init__(self, message: str, is_user: bool = True, message_type: str = "text"):
        super().__init__()
        self.message = message
        self.is_user = is_user
        self.message_type = message_type
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(1)
        
        # 设置样式 - 使用新的设计系统
        if self.is_user:
            self.setStyleSheet(f"""
                ChatBubble {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {ModernDesignSystem.get_color('primary.main')},
                        stop:1 {ModernDesignSystem.get_color('primary.light')});
                    color: white;
                    border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                    padding: {ModernDesignSystem.get_spacing('md')}px;
                    margin: {ModernDesignSystem.get_spacing('sm')}px;
                }}
            """)
        else:
            self.setStyleSheet(f"""
                ChatBubble {{
                    background-color: {ModernDesignSystem.get_color('background.primary')};
                    color: {ModernDesignSystem.get_color('neutral.gray_800')};
                    border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                    border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                    padding: {ModernDesignSystem.get_spacing('md')}px;
                    margin: {ModernDesignSystem.get_spacing('sm')}px;
                }}
            """)
        
        # 布局
        layout = QVBoxLayout()
        
        # 消息内容
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setFont(QFont("Microsoft YaHei", 10))
        
        # 时间戳
        timestamp = QLabel(datetime.now().strftime("%H:%M"))
        timestamp.setFont(QFont("Microsoft YaHei", 8))
        timestamp.setStyleSheet("color: gray;")
        
        if self.is_user:
            timestamp.setAlignment(Qt.AlignmentFlag.AlignRight)
        else:
            timestamp.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        layout.addWidget(message_label)
        layout.addWidget(timestamp)
        self.setLayout(layout)
        
        # 设置最大宽度
        self.setMaximumWidth(400)


class DocumentChatGUI(QMainWindow):
    """智能文档处理系统主界面"""
    
    def __init__(self):
        super().__init__()
        self.agent = None
        self.current_session = None
        self.worker = None
        self.setup_ui()
        self.setup_connections()
        self.initialize_agent()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("智能文档处理系统 - AI文档助手")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置应用图标
        self.setWindowIcon(QIcon("icon.png"))  # 如果有图标文件
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧主聊天区域
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        # 创建状态栏
        self.setup_status_bar()
        
        # 创建工具栏
        self.setup_toolbar()
        
        # 设置样式 - 使用新的设计系统
        combined_styles = (
            ModernStyleSheets.get_main_window_style() +
            ModernStyleSheets.get_button_style() +
            ModernStyleSheets.get_input_style() +
            ModernStyleSheets.get_scrollbar_style() +
            ModernStyleSheets.get_group_box_style() +
            ModernStyleSheets.get_combo_box_style()
        )
        self.setStyleSheet(combined_styles)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        left_panel = QWidget()
        left_panel.setFixedWidth(320)  # 稍微增加宽度
        left_panel.setStyleSheet(f"background-color: {ModernDesignSystem.get_color('background.primary')};")
        
        layout = QVBoxLayout()
        left_panel.setLayout(layout)
        
        # 标题
        title_label = QLabel("🎯 智能文档助手")
        title_label.setFont(ModernDesignSystem.get_font('lg', 'bold'))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('primary.main')}; padding: {ModernDesignSystem.get_spacing('md')}px;")
        layout.addWidget(title_label)
        
        # 文档类型选择
        doc_type_group = QGroupBox("📋 文档类型")
        doc_type_layout = QVBoxLayout()
        
        self.doc_type_combo = QComboBox()
        self.doc_type_combo.addItems([
            "商务报告", "技术文档", "学术论文", "用户手册", 
            "项目计划", "会议纪要", "产品介绍", "培训手册",
            "法律文件", "财务报告", "营销方案", "研究报告"
        ])
        doc_type_layout.addWidget(self.doc_type_combo)
        doc_type_group.setLayout(doc_type_layout)
        layout.addWidget(doc_type_group)
        
        # 主题选择
        theme_group = QGroupBox("🎨 文档主题")
        theme_layout = QVBoxLayout()
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems([
            "professional - 专业商务",
            "modern - 现代科技", 
            "elegant - 优雅正式",
            "academic - 学术研究",
            "creative - 创意设计",
            "minimal - 简约风格"
        ])
        theme_layout.addWidget(self.theme_combo)
        theme_group.setLayout(theme_layout)
        layout.addWidget(theme_group)
        
        # 设置选项
        settings_group = QGroupBox("⚙️ 设置选项")
        settings_layout = QVBoxLayout()
        
        self.auto_save_cb = QCheckBox("自动保存文档")
        self.auto_save_cb.setChecked(True)
        settings_layout.addWidget(self.auto_save_cb)
        
        self.wps_visible_cb = QCheckBox("显示WPS窗口")
        self.wps_visible_cb.setChecked(True)
        settings_layout.addWidget(self.wps_visible_cb)
        
        self.include_toc_cb = QCheckBox("包含目录")
        self.include_toc_cb.setChecked(True)
        settings_layout.addWidget(self.include_toc_cb)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # 快捷操作按钮
        quick_actions_group = QGroupBox("🚀 快捷操作")
        quick_actions_layout = QVBoxLayout()
        
        self.new_session_btn = QPushButton("新建会话")
        self.new_session_btn.setIcon(QIcon("new.png"))
        quick_actions_layout.addWidget(self.new_session_btn)
        
        self.clear_history_btn = QPushButton("清空历史")
        self.clear_history_btn.setIcon(QIcon("clear.png"))
        quick_actions_layout.addWidget(self.clear_history_btn)
        
        self.export_chat_btn = QPushButton("导出对话")
        self.export_chat_btn.setIcon(QIcon("export.png"))
        quick_actions_layout.addWidget(self.export_chat_btn)
        
        quick_actions_group.setLayout(quick_actions_layout)
        layout.addWidget(quick_actions_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 版本信息
        version_label = QLabel("版本: v2.0\n基于DeepSeek API")
        version_label.setFont(QFont("Microsoft YaHei", 8))
        version_label.setStyleSheet("color: gray; padding: 10px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_label)
        
        return left_panel
    
    def create_right_panel(self) -> QWidget:
        """创建右侧主聊天区域"""
        right_panel = QWidget()
        layout = QVBoxLayout()
        right_panel.setLayout(layout)
        
        # 聊天历史区域
        self.chat_area = QScrollArea()
        self.chat_area.setWidgetResizable(True)
        self.chat_area.setStyleSheet("background-color: white; border: 1px solid #CCCCCC;")
        
        # 聊天内容容器
        self.chat_widget = QWidget()
        self.chat_layout = QVBoxLayout()
        self.chat_widget.setLayout(self.chat_layout)
        self.chat_area.setWidget(self.chat_widget)
        
        layout.addWidget(self.chat_area)
        
        # 进度显示区域
        self.progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_text = QLabel("准备就绪")
        self.progress_text.setFont(QFont("Microsoft YaHei", 9))
        self.progress_text.setStyleSheet("color: #666;")
        progress_layout.addWidget(self.progress_text)
        
        self.progress_group.setLayout(progress_layout)
        layout.addWidget(self.progress_group)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("请输入您的文档创建需求，例如：创建一份软件测试报告...")
        self.input_field.setFont(QFont("Microsoft YaHei", 10))
        self.input_field.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #CCCCCC;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007ACC;
            }
        """)
        input_layout.addWidget(self.input_field)
        
        self.send_btn = QPushButton("发送")
        self.send_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.send_btn.setFixedSize(80, 40)
        input_layout.addWidget(self.send_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.stop_btn.setFixedSize(80, 40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)
        input_layout.addWidget(self.stop_btn)
        
        layout.addLayout(input_layout)
        
        # 添加欢迎消息
        self.add_welcome_message()
        
        return right_panel
    
    def add_welcome_message(self):
        """添加欢迎消息"""
        welcome_msg = """🎉 欢迎使用智能文档处理系统！

我是您的AI文档助手，可以帮您创建各种专业文档：

📋 支持的文档类型：
• 商务报告 • 技术文档 • 学术论文 • 用户手册
• 项目计划 • 会议纪要 • 产品介绍 • 培训手册

🎨 可选主题风格：
• Professional - 专业商务风格
• Modern - 现代科技风格  
• Elegant - 优雅正式风格
• Academic - 学术研究风格
• Creative - 创意设计风格
• Minimal - 简约风格

💡 使用建议：
请详细描述您的文档需求，包括：
- 文档类型和用途
- 主要内容和章节
- 预期字数
- 特殊要求

例如："创建一份软件测试报告，包含测试计划、测试用例、测试结果等章节，大约3000字"

开始对话，让我为您创建专业文档！✨"""
        
        bubble = ChatBubble(welcome_msg, is_user=False)
        self.chat_layout.addWidget(bubble)
        
        # 滚动到底部
        QTimer.singleShot(100, self.scroll_to_bottom)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加状态信息
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 添加会话信息
        self.session_label = QLabel("会话: 未连接")
        self.status_bar.addPermanentWidget(self.session_label)
        
        # 添加API状态
        self.api_label = QLabel("API: 未连接")
        self.status_bar.addPermanentWidget(self.api_label)
    
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 新建会话
        new_action = QAction("新建会话", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_session)
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        # 保存对话
        save_action = QAction("保存对话", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_conversation)
        toolbar.addAction(save_action)
        
        # 加载对话
        load_action = QAction("加载对话", self)
        load_action.setShortcut("Ctrl+O")
        load_action.triggered.connect(self.load_conversation)
        toolbar.addAction(load_action)
        
        toolbar.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        # 帮助
        help_action = QAction("帮助", self)
        help_action.triggered.connect(self.show_help)
        toolbar.addAction(help_action)
    
    def setup_connections(self):
        """设置信号连接"""
        # 发送按钮
        self.send_btn.clicked.connect(self.send_message)
        
        # 停止按钮
        self.stop_btn.clicked.connect(self.stop_processing)
        
        # 回车发送
        self.input_field.returnPressed.connect(self.send_message)
        
        # 快捷按钮
        self.new_session_btn.clicked.connect(self.new_session)
        self.clear_history_btn.clicked.connect(self.clear_history)
        self.export_chat_btn.clicked.connect(self.export_conversation)
    
    def initialize_agent(self):
        """初始化AI Agent"""
        try:
            # 检查配置文件
            if not os.path.exists("config_deepseek.json"):
                QMessageBox.warning(self, "配置文件缺失", 
                                "找不到 config_deepseek.json 配置文件！\n请确保配置文件存在并包含正确的API密钥。")
                return
            
            # 初始化Agent
            self.agent = IntelligentDocumentAgentV2()
            
            # 创建新会话 - 修复：使用正确的方法名
            self.current_session = self.agent.start_conversation("user_001")
            
            # 更新状态
            self.status_label.setText("就绪")
            self.session_label.setText(f"会话: {self.current_session}")
            self.api_label.setText("API: 已连接")
            
            # 添加成功消息
            self.add_system_message("✅ 系统初始化成功，可以开始创建文档了！")
            
        except Exception as e:
            QMessageBox.critical(self, "初始化错误", f"初始化AI Agent失败：\n{str(e)}")
            self.status_label.setText("初始化失败")
            self.api_label.setText("API: 连接失败")
    
    def send_message(self):
        """发送消息"""
        user_input = self.input_field.text().strip()
        if not user_input:
            return
        
        if not self.agent:
            QMessageBox.warning(self, "系统未就绪", "AI Agent未初始化，请重启程序！")
            return
        
        # 清空输入框
        self.input_field.clear()
        
        # 添加用户消息到界面
        self.add_user_message(user_input)
        
        # 开始处理
        self.start_processing(user_input)
    
    def add_user_message(self, message: str):
        """添加用户消息"""
        bubble = ChatBubble(message, is_user=True)
        self.chat_layout.addWidget(bubble)
        self.scroll_to_bottom()
    
    def add_ai_message(self, message: str):
        """添加AI消息"""
        bubble = ChatBubble(message, is_user=False)
        self.chat_layout.addWidget(bubble)
        self.scroll_to_bottom()
    
    def add_system_message(self, message: str):
        """添加系统消息"""
        bubble = ChatBubble(f"🔔 系统消息: {message}", is_user=False)
        bubble.setStyleSheet("""
            ChatBubble {
                background-color: #FFF3CD;
                color: #856404;
                border: 1px solid #FFEAA7;
                border-radius: 15px;
                padding: 10px;
                margin: 5px;
            }
        """)
        self.chat_layout.addWidget(bubble)
        self.scroll_to_bottom()
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        QTimer.singleShot(50, lambda: self.chat_area.verticalScrollBar().setValue(
            self.chat_area.verticalScrollBar().maximum()))
    
    def start_processing(self, user_input: str):
        """开始处理用户输入"""
        # 禁用发送按钮，启用停止按钮
        self.send_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不定进度
        self.progress_text.setText("正在处理您的请求...")
        
        # 创建工作线程
        self.worker = DocumentWorker(self.agent, user_input, self.current_session)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.document_completed.connect(self.on_document_completed)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.finished.connect(self.on_processing_finished)
        
        # 启动线程
        self.worker.start()
    
    def stop_processing(self):
        """停止处理"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.add_system_message("⏹️ 处理已停止")
    
    @pyqtSlot(str, str)
    def update_progress(self, stage: str, message: str):
        """更新进度"""
        # 更新进度文本
        self.progress_text.setText(f"[{stage}] {message}")
        
        # 在聊天区域显示进度（可选）
        if stage in ['analysis', 'execution']:
            progress_msg = f"📊 {message}"
            bubble = ChatBubble(progress_msg, is_user=False)
            bubble.setStyleSheet("""
                ChatBubble {
                    background-color: #E3F2FD;
                    color: #1976D2;
                    border: 1px solid #BBDEFB;
                    border-radius: 15px;
                    padding: 10px;
                    margin: 5px;
                }
            """)
            self.chat_layout.addWidget(bubble)
            self.scroll_to_bottom()
    
    @pyqtSlot(dict)
    def on_document_completed(self, result: dict):
        """文档完成处理"""
        ai_response = result.get('ai_response', '文档创建完成！')
        self.add_ai_message(ai_response)
        
        # 显示成功消息
        self.add_system_message("🎉 文档创建成功！")
        
        # 如果有文档路径，显示文档信息
        if 'document_plan' in result:
            plan = result['document_plan']
            file_name = plan.get('file_name', '文档')
            self.add_system_message(f"📄 文档已保存为: {file_name}")
    
    @pyqtSlot(str)
    def on_error(self, error_message: str):
        """错误处理"""
        self.add_system_message(f"❌ 发生错误: {error_message}")
        QMessageBox.critical(self, "处理错误", f"处理请求时发生错误：\n{error_message}")
    
    def on_processing_finished(self):
        """处理完成"""
        # 恢复按钮状态
        self.send_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.progress_text.setText("就绪")
        
        # 清理工作线程
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
    
    def new_session(self):
        """新建会话"""
        if self.agent:
            # 修复：使用正确的方法名
            self.current_session = self.agent.start_conversation("user_001")
            self.session_label.setText(f"会话: {self.current_session}")
            self.clear_history()
            self.add_system_message("🆕 新会话已创建")
    
    def clear_history(self):
        """清空历史"""
        # 清空聊天布局
        for i in reversed(range(self.chat_layout.count())): 
            self.chat_layout.itemAt(i).widget().setParent(None)
        
        # 添加欢迎消息
        self.add_welcome_message()
    
    def export_conversation(self):
        """导出对话"""
        if not self.current_session:
            QMessageBox.warning(self, "无会话", "当前没有活动会话可导出！")
            return
        
        try:
            # 选择保存位置
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出对话", f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )
            
            if file_path:
                # 获取对话数据
                conversation = self.agent.conversations.get(self.current_session)
                if conversation:
                    export_data = {
                        "session_id": self.current_session,
                        "created_at": conversation.created_at.isoformat(),
                        "messages": conversation.messages,
                        "document_plan": conversation.document_plan
                    }
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, ensure_ascii=False, indent=2)
                    
                    QMessageBox.information(self, "导出成功", f"对话已导出到：\n{file_path}")
                else:
                    QMessageBox.warning(self, "导出失败", "找不到会话数据！")
                    
        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出对话失败：\n{str(e)}")
    
    def save_conversation(self):
        """保存对话"""
        self.export_conversation()
    
    def load_conversation(self):
        """加载对话"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载对话", "", "JSON Files (*.json)"
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 清空当前历史
                self.clear_history()
                
                # 重现对话
                messages = data.get('messages', [])
                for msg in messages:
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    
                    if role == 'user':
                        self.add_user_message(content)
                    else:
                        self.add_ai_message(content)
                
                QMessageBox.information(self, "加载成功", "对话历史已加载！")
                
        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载对话失败：\n{str(e)}")
    
    def show_settings(self):
        """显示设置"""
        QMessageBox.information(self, "设置", "设置功能正在开发中...")
    
    def show_help(self):
        """显示帮助"""
        help_text = """
        🎯 智能文档处理系统使用帮助
        
        📋 基本功能：
        • 创建各种类型的专业文档
        • 支持多种主题风格
        • 自动生成文档结构和内容
        • 实时显示处理进度
        
        💡 使用技巧：
        • 详细描述文档需求
        • 选择合适的文档类型和主题
        • 可以随时停止处理过程
        • 支持对话历史导出
        
        🔧 快捷键：
        • Ctrl+N: 新建会话
        • Ctrl+S: 保存对话
        • Ctrl+O: 加载对话
        • Enter: 发送消息
        
        ❓ 如需帮助，请联系开发团队
        """
        QMessageBox.information(self, "使用帮助", help_text)
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止工作线程
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
        
        # 关闭Agent连接
        if self.agent:
            # 这里可以添加清理代码
            pass
        
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用属性
    app.setApplicationName("智能文档处理系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AI Document Assistant")
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示主窗口
    window = DocumentChatGUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 