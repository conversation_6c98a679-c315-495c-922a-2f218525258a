# -*- coding: utf-8 -*-
"""
增强版WPS文档操作工具 - 专为AI Agent使用优化

本模块提供了完整的WPS文档操作功能，包括：
- 文档创建、打开、保存
- 文本操作（添加、替换、格式化）
- 表格操作（创建、填充、格式化）
- 图片插入和处理
- 页眉页脚设置
- 目录生成
- 批量文档处理
- 模板填充

主要特点：
1. 面向Agent的友好API设计
2. 完整的错误处理和状态返回
3. 支持上下文管理器，自动资源清理
4. 丰富的样式和格式支持
5. 批量处理能力

依赖：
- pywin32: Windows COM接口支持
- 需要安装WPS Office
"""

import win32com.client as win32  # Windows COM接口，用于控制WPS应用
import os  # 文件和目录操作
import json  # JSON数据处理
import re  # 正则表达式处理
from typing import Optional, List, Dict, Any, Tuple, Union  # 类型提示
from dataclasses import dataclass, asdict  # 数据类，用于结构化配置
from contextlib import contextmanager  # 上下文管理器支持
from datetime import datetime  # 日期时间处理
import uuid  # 生成唯一标识符


@dataclass
class TableData:
    """表格数据结构类
    
    用于定义和传递表格数据的配置信息
    
    属性:
        rows: 表格行数
        cols: 表格列数
        data: 二维数组，存储表格内容，格式为[row][col]
        style: 表格样式名称（可选）
        auto_fit: 是否自动调整列宽，默认为True
    """
    rows: int
    cols: int
    data: List[List[str]]  # 二维数组，[row][col]
    style: Optional[str] = None
    auto_fit: bool = True


@dataclass
class DocumentConfig:
    """文档配置类
    
    用于配置WPS文档的打开和显示行为
    
    属性:
        visible: 是否显示WPS窗口，默认为True
        auto_save: 是否自动保存，默认为True
        backup: 是否创建备份，默认为False
        read_only: 是否以只读模式打开，默认为False
    """
    visible: bool = True
    auto_save: bool = True
    backup: bool = False
    read_only: bool = False


@dataclass
class TextStyle:
    """文本样式配置类 - 增强版
    
    用于定义文本的显示样式，支持丰富的格式选项
    
    属性:
        font_name: 字体名称，默认为"宋体"
        font_size: 字体大小（磅），默认为12
        bold: 是否加粗，默认为False
        italic: 是否斜体，默认为False
        underline: 是否下划线，默认为False
        strikethrough: 是否删除线，默认为False
        color: 文字颜色，RGB格式字符串，如"255,0,0"表示红色
        highlight_color: 高亮背景色，RGB格式字符串
        alignment: 对齐方式，可选值：left, center, right, justify
        font_style: 字体样式，可选值：regular, bold, italic, bold_italic
        text_effect: 文字效果，可选值：shadow, outline, emboss, engrave
        character_spacing: 字符间距（磅），默认为0
        vertical_position: 垂直位置，可选值：normal, superscript, subscript
    """
    font_name: str = "宋体"
    font_size: int = 12
    bold: bool = False
    italic: bool = False
    underline: bool = False
    strikethrough: bool = False
    color: Optional[str] = None  # RGB格式，如 "255,0,0"
    highlight_color: Optional[str] = None  # RGB格式高亮色
    alignment: str = "left"  # left, center, right, justify
    font_style: str = "regular"  # regular, bold, italic, bold_italic
    text_effect: Optional[str] = None  # shadow, outline, emboss, engrave
    character_spacing: float = 0.0  # 字符间距（磅）
    vertical_position: str = "normal"  # normal, superscript, subscript


@dataclass
class ParagraphFormat:
    """段落格式配置类 - 增强版
    
    用于定义段落的布局和间距，支持丰富的段落格式
    
    属性:
        line_spacing: 行间距倍数，默认为1.0
        space_before: 段前间距（磅），默认为0.0
        space_after: 段后间距（磅），默认为0.0
        first_line_indent: 首行缩进（磅），默认为0.0
        left_indent: 左缩进（磅），默认为0.0
        right_indent: 右缩进（磅），默认为0.0
        alignment: 段落对齐方式，可选值：left, center, right, justify, distribute
        outline_level: 大纲级别，1-9或None
        keep_with_next: 与下段同页，默认为False
        keep_lines_together: 段中不分页，默认为False
        page_break_before: 段前分页，默认为False
        widow_control: 孤行控制，默认为True
        tab_stops: 制表位列表，格式：[(位置, 类型), ...]
        border_style: 边框样式，可选值：none, single, double, dotted, dashed
        shading_color: 段落底纹颜色，RGB格式字符串
    """
    line_spacing: float = 1.0
    space_before: float = 0.0
    space_after: float = 0.0
    first_line_indent: float = 0.0
    left_indent: float = 0.0
    right_indent: float = 0.0
    alignment: str = "left"  # left, center, right, justify, distribute
    outline_level: Optional[int] = None  # 1-9或None
    keep_with_next: bool = False
    keep_lines_together: bool = False
    page_break_before: bool = False
    widow_control: bool = True
    tab_stops: Optional[List[Tuple[float, str]]] = None  # [(位置, 类型), ...]
    border_style: str = "none"  # none, single, double, dotted, dashed
    shading_color: Optional[str] = None  # RGB格式


@dataclass
class DocumentMetadata:
    """文档元数据类
    
    用于设置文档的属性信息
    
    属性:
        title: 文档标题
        subject: 主题
        author: 作者
        keywords: 关键词
        description: 描述
        category: 分类
        company: 公司
    """
    title: Optional[str] = None
    subject: Optional[str] = None
    author: Optional[str] = None
    keywords: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    company: Optional[str] = None


@dataclass
class PageStyle:
    """页面样式配置类
    
    用于设置页面布局和页面属性
    
    属性:
        page_size: 页面大小，可选值：A4, A3, A5, Letter, Legal, custom
        orientation: 页面方向，可选值：portrait, landscape
        margin_top: 上边距（厘米），默认为2.54
        margin_bottom: 下边距（厘米），默认为2.54
        margin_left: 左边距（厘米），默认为3.18
        margin_right: 右边距（厘米），默认为3.18
        header_distance: 页眉距顶端距离（厘米），默认为1.27
        footer_distance: 页脚距底端距离（厘米），默认为1.27
        custom_width: 自定义页面宽度（厘米），仅当page_size为custom时有效
        custom_height: 自定义页面高度（厘米），仅当page_size为custom时有效
        gutter: 装订线宽度（厘米），默认为0
        mirror_margins: 是否镜像页边距，默认为False
        two_pages_on_one: 双页打印，默认为False
    """
    page_size: str = "A4"  # A4, A3, A5, Letter, Legal, custom
    orientation: str = "portrait"  # portrait, landscape
    margin_top: float = 2.54
    margin_bottom: float = 2.54
    margin_left: float = 3.18
    margin_right: float = 3.18
    header_distance: float = 1.27
    footer_distance: float = 1.27
    custom_width: Optional[float] = None  # 仅custom时使用
    custom_height: Optional[float] = None  # 仅custom时使用
    gutter: float = 0.0
    mirror_margins: bool = False
    two_pages_on_one: bool = False


@dataclass
class HeaderFooterStyle:
    """页眉页脚样式配置类
    
    用于设置页眉页脚的内容和样式
    
    属性:
        header_text: 页眉文本内容
        footer_text: 页脚文本内容
        header_alignment: 页眉对齐方式，可选值：left, center, right
        footer_alignment: 页脚对齐方式，可选值：left, center, right
        include_page_number: 是否包含页码，默认为True
        page_number_format: 页码格式，可选值：arabic, roman_upper, roman_lower, alpha_upper, alpha_lower
        page_number_position: 页码位置，可选值：header_left, header_center, header_right, footer_left, footer_center, footer_right
        header_style: 页眉文本样式
        footer_style: 页脚文本样式
        first_page_different: 首页不同，默认为False
        odd_even_different: 奇偶页不同，默认为False
        border_line: 是否显示边框线，默认为False
    """
    header_text: Optional[str] = None
    footer_text: Optional[str] = None
    header_alignment: str = "center"  # left, center, right
    footer_alignment: str = "center"  # left, center, right
    include_page_number: bool = True
    page_number_format: str = "arabic"  # arabic, roman_upper, roman_lower, alpha_upper, alpha_lower
    page_number_position: str = "footer_center"  # header_left, header_center, header_right, footer_left, footer_center, footer_right
    header_style: Optional[TextStyle] = None
    footer_style: Optional[TextStyle] = None
    first_page_different: bool = False
    odd_even_different: bool = False
    border_line: bool = False


@dataclass
class DocumentTheme:
    """文档主题配置类
    
    用于设置文档的整体主题和风格
    
    属性:
        theme_name: 主题名称，可选值：professional, modern, elegant, academic, creative, minimal
        primary_color: 主色调，RGB格式字符串
        secondary_color: 辅助色，RGB格式字符串
        accent_color: 强调色，RGB格式字符串
        heading_font: 标题字体
        body_font: 正文字体
        heading_styles: 各级标题样式字典 {level: TextStyle}
        default_paragraph_format: 默认段落格式
        color_scheme: 颜色方案，包含多种预定义颜色
        apply_to_tables: 是否应用到表格样式，默认为True
        apply_to_charts: 是否应用到图表样式，默认为True
    """
    theme_name: str = "professional"  # professional, modern, elegant, academic, creative, minimal
    primary_color: str = "79,129,189"  # RGB格式
    secondary_color: str = "192,192,192"  # RGB格式
    accent_color: str = "247,150,70"  # RGB格式
    heading_font: str = "微软雅黑"
    body_font: str = "宋体"
    heading_styles: Optional[Dict[int, TextStyle]] = None  # {level: TextStyle}
    default_paragraph_format: Optional[ParagraphFormat] = None
    color_scheme: Optional[Dict[str, str]] = None  # 预定义颜色方案
    apply_to_tables: bool = True
    apply_to_charts: bool = True


class WPSDocumentTool:
    """增强版WPS文档操作工具类 - 专为Agent使用优化
    
    这是核心的文档操作类，提供了完整的WPS文档控制功能。
    支持上下文管理器，确保资源正确释放。
    
    主要功能：
    1. 文档生命周期管理（创建、打开、保存、关闭）
    2. 文本操作（添加、替换、格式化）
    3. 表格操作（创建、填充、样式设置）
    4. 图片处理（插入、调整大小）
    5. 文档格式化（页眉页脚、目录、样式）
    6. 元数据管理
    
    使用示例：
        with WPSDocumentTool() as tool:
            tool.create_document()
            tool.add_text("Hello World")
            tool.save_document("output.docx")
    """

    def __init__(self, config: DocumentConfig = None):
        """初始化WPS文档工具
        
        参数:
            config: 文档配置对象，如果为None则使用默认配置
        """
        self.config = config or DocumentConfig()  # 使用默认配置或传入配置
        self.wps = None  # WPS应用实例
        self.current_doc = None  # 当前打开的文档
        self._selection = None  # 当前选区（预留）

    def __enter__(self):
        """上下文管理器入口
        
        使用with语句时自动调用，建立WPS连接
        """
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出
        
        退出with语句块时自动调用，确保资源清理
        参数:
            exc_type: 异常类型
            exc_val: 异常值
            exc_tb: 异常回溯
        """
        self.disconnect()

    def connect(self):
        """连接WPS应用
        
        通过COM接口连接到WPS Office应用程序
        
        返回:
            bool: 连接成功返回True
            
        异常:
            Exception: 连接失败时抛出异常
        """
        try:
            # 创建WPS应用实例
            self.wps = win32.Dispatch('Kwps.Application')
            self.wps.Visible = self.config.visible  # 设置可见性
            self.wps.DisplayAlerts = False  # 禁用警告提示，避免干扰自动化
            return True
        except Exception as e:
            raise Exception(f"连接WPS失败: {str(e)}")

    def disconnect(self):
        """断开WPS连接
        
        安全地关闭文档和WPS应用，释放资源
        确保不会留下后台进程
        """
        try:
            # 关闭当前文档（不保存）
            if self.current_doc:
                self.current_doc.Close(SaveChanges=False)
                self.current_doc = None
            # 退出WPS应用
            if self.wps:
                self.wps.Quit()
                self.wps = None
        except Exception as e:
            print(f"断开连接时出错: {e}")

    def create_document(self, template_path: Optional[str] = None) -> Dict[str, Any]:
        """创建新文档
        
        参数:
            template_path: 模板文件路径，如果提供则基于模板创建
            
        返回:
            Dict: 包含操作结果的字典
            {
                "success": bool,  # 操作是否成功
                "document_name": str,  # 文档名称
                "document_path": str  # 文档路径
            }
        """
        try:
            if template_path and os.path.exists(template_path):
                # 基于模板创建文档
                self.current_doc = self.wps.Documents.Add(template_path)
            else:
                # 创建空白文档
                self.current_doc = self.wps.Documents.Add()

            return {
                "success": True,
                "document_name": self.current_doc.Name,
                "document_path": self.current_doc.Path
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def open_document(self, file_path: str) -> Dict[str, Any]:
        """打开已存在的文档
        
        参数:
            file_path: 文档文件路径
            
        返回:
            Dict: 包含操作结果和文档信息的字典
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 打开文档
            self.current_doc = self.wps.Documents.Open(
                file_path,
                ReadOnly=self.config.read_only
            )

            return {
                "success": True,
                "document_name": self.current_doc.Name,
                "document_path": self.current_doc.Path,
                "info": self.get_document_info()
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_text(self, text: str, position: str = "end", style: Optional[TextStyle] = None) -> Dict[str, Any]:
        """添加文本，支持样式设置
        
        参数:
            text: 要添加的文本内容
            position: 插入位置，可选值：
                     "start" - 文档开头
                     "end" - 文档末尾（默认）
                     整数 - 指定字符位置
            style: 文本样式配置对象
            
        返回:
            Dict: 包含操作结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 确保文本以换行结尾（如果不是已经以换行结尾）
            if not text.endswith('\n'):
                text += '\n'

            # 确定插入位置
            if position == "start":
                range_obj = self.current_doc.Range(0, 0)
                range_obj.Text = text
            elif position == "end":
                # 修复：使用Selection对象确保正确追加
                selection = self.wps.Selection
                selection.EndKey(6)  # wdStory - 移动到文档末尾
                selection.Collapse(0)  # 确保没有选中任何文本，只是光标位置
                selection.TypeText(text)
                
                # 获取刚插入的文本范围用于应用样式
                start_pos = selection.End - len(text)
                range_obj = self.current_doc.Range(start_pos, selection.End)
            elif isinstance(position, int):
                # 指定位置插入
                if position >= len(self.current_doc.Content.Text):
                    # 位置超出文档长度，追加到末尾
                    selection = self.wps.Selection
                    selection.EndKey(6)
                    selection.Collapse(0)  # 确保没有选中任何文本
                    selection.TypeText(text)
                    start_pos = selection.End - len(text)
                    range_obj = self.current_doc.Range(start_pos, selection.End)
                else:
                    range_obj = self.current_doc.Range(position, position)
                    range_obj.Text = text
            else:
                # 默认追加到末尾
                selection = self.wps.Selection
                selection.EndKey(6)
                selection.Collapse(0)  # 确保没有选中任何文本
                selection.TypeText(text)
                start_pos = selection.End - len(text)
                range_obj = self.current_doc.Range(start_pos, selection.End)

            # 应用样式
            if style:
                self._apply_style_to_range(range_obj, style)

            return {"success": True, "text_length": len(text)}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def append_text_safe(self, text: str, style: Optional[TextStyle] = None) -> Dict[str, Any]:
        """安全地追加文本到文档末尾
        
        使用Selection对象确保文本正确追加而不覆盖现有内容
        
        参数:
            text: 要追加的文本内容
            style: 文本样式配置对象
            
        返回:
            Dict: 包含操作结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 使用Selection对象移动到文档末尾
            selection = self.wps.Selection
            selection.EndKey(6)  # wdStory - 移动到文档末尾
            
            # 直接输入文本
            selection.TypeText(text)
            
            # 如果需要应用样式，选择刚输入的文本并应用样式
            if style:
                # 选择刚输入的文本
                start_pos = selection.End - len(text)
                range_obj = self.current_doc.Range(start_pos, selection.End)
                self._apply_style_to_range(range_obj, style)

            return {"success": True, "text_length": len(text)}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_paragraph(self, text: str, style: Optional[TextStyle] = None,
                     paragraph_format: Optional[ParagraphFormat] = None) -> Dict[str, Any]:
        """添加段落
        
        在文档末尾添加一个新的段落，支持文本样式和段落格式
        
        参数:
            text: 段落文本内容
            style: 文本样式配置
            paragraph_format: 段落格式配置
            
        返回:
            Dict: 包含段落索引的操作结果
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 在文档末尾添加段落
            range_obj = self.current_doc.Range()
            if range_obj.Text.endswith('\r'):
                range_obj.Collapse(0)  # 折叠到末尾
            else:
                range_obj.Text += "\r"
                range_obj.Collapse(0)

            # 添加文本和换行
            range_obj.Text = text + "\r"

            # 应用文本样式
            if style:
                self._apply_style_to_range(range_obj, style)

            # 应用段落格式
            if paragraph_format:
                self._apply_paragraph_format(range_obj, paragraph_format)

            return {"success": True, "paragraph_index": self.current_doc.Paragraphs.Count}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def replace_text(self, old_text: str, new_text: str, case_sensitive: bool = True) -> Dict[str, Any]:
        """智能替换文本，支持正则表达式
        
        参数:
            old_text: 要替换的文本
            new_text: 替换后的文本
            case_sensitive: 是否区分大小写，默认为True
            
        返回:
            Dict: 包含替换统计信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 配置查找对象
            find_obj = self.current_doc.Content.Find
            find_obj.Text = old_text
            find_obj.Replacement.Text = new_text
            find_obj.Forward = True
            find_obj.Wrap = 1  # wdFindContinue
            find_obj.Format = False
            find_obj.MatchCase = case_sensitive
            find_obj.MatchWholeWord = False
            find_obj.MatchWildcards = False
            find_obj.MatchSoundsLike = False
            find_obj.MatchAllWordForms = False

            # 执行替换
            replaced_count = 0
            while find_obj.Execute(Replace=2):  # 2 = wdReplaceAll
                replaced_count += 1

            return {
                "success": True,
                "replaced_count": replaced_count,
                "pattern": old_text,
                "replacement": new_text
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def replace_with_regex(self, pattern: str, replacement: str,
                          case_sensitive: bool = True) -> Dict[str, Any]:
        """使用正则表达式替换文本
        
        参数:
            pattern: 正则表达式模式
            replacement: 替换字符串
            case_sensitive: 是否区分大小写
            
        返回:
            Dict: 包含替换统计信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 获取全部内容
            content = self.current_doc.Content.Text
            flags = 0 if case_sensitive else re.IGNORECASE
            
            # 执行正则替换
            new_content, count = re.subn(pattern, replacement, content, flags=flags)

            if count > 0:
                self.current_doc.Content.Text = new_content

            return {
                "success": True,
                "replaced_count": count,
                "pattern": pattern,
                "replacement": replacement
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def create_table(self, table_data: TableData, position: str = "end",
                    caption: Optional[str] = None) -> Dict[str, Any]:
        """创建表格，支持标题和样式
        
        参数:
            table_data: 表格数据对象
            position: 插入位置，"start"或"end"
            caption: 表格标题（可选）
            
        返回:
            Dict: 包含表格信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 确定插入位置 - 修复：正确定位到文档末尾而不覆盖现有内容
            if position == "end":
                # 使用Selection对象移动到文档末尾
                selection = self.wps.Selection
                selection.EndKey(6)  # wdStory - 移动到文档末尾
                selection.Collapse(0)  # 确保没有选中任何文本
                
                # 添加表格标题
                if caption:
                    selection.TypeText(f"\n{caption}\n")
                
                # 创建插入点Range
                range_obj = selection.Range
            elif position == "start":
                range_obj = self.current_doc.Range(0, 0)
                # 添加表格标题
                if caption:
                    range_obj.Text = f"{caption}\n"
                    range_obj.Collapse(0)
            else:
                # 默认在末尾
                selection = self.wps.Selection
                selection.EndKey(6)  # wdStory - 移动到文档末尾
                selection.Collapse(0)  # 确保没有选中任何文本
                
                # 添加表格标题
                if caption:
                    selection.TypeText(f"\n{caption}\n")
                
                # 创建插入点Range
                range_obj = selection.Range

            # 创建表格
            table = self.current_doc.Tables.Add(range_obj, table_data.rows, table_data.cols)

            # 填充表格数据
            for row_idx, row_data in enumerate(table_data.data):
                for col_idx, cell_data in enumerate(row_data):
                    if row_idx < table_data.rows and col_idx < table_data.cols:
                        cell = table.Cell(row_idx + 1, col_idx + 1)
                        cell.Range.Text = str(cell_data)

            # 应用表格样式
            if table_data.style:
                table.Style = table_data.style

            # 自动调整列宽
            if table_data.auto_fit:
                table.AutoFitBehavior(1)  # wdAutoFitContent

            return {
                "success": True,
                "table_index": self.current_doc.Tables.Count,
                "rows": table_data.rows,
                "cols": table_data.cols
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def format_text(self, text: str, style: TextStyle) -> Dict[str, Any]:
        """格式化指定文本
        
        在文档中查找指定文本并应用样式
        
        参数:
            text: 要格式化的文本
            style: 要应用的样式
            
        返回:
            Dict: 包含格式化统计信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            find_obj = self.current_doc.Content.Find
            find_obj.Text = text
            find_obj.Forward = True
            find_obj.Wrap = 1

            formatted_count = 0
            while find_obj.Execute():
                range_obj = find_obj.Parent
                self._apply_style_to_range(range_obj, style)
                formatted_count += 1

            return {
                "success": True,
                "formatted_count": formatted_count,
                "text": text,
                "style": asdict(style)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def insert_image(self, image_path: str, position: str = "end",
                    width: Optional[float] = None, height: Optional[float] = None,
                    caption: Optional[str] = None) -> Dict[str, Any]:
        """插入图片，支持尺寸调整和标题
        
        参数:
            image_path: 图片文件路径
            position: 插入位置，"start"或"end"
            width: 图片宽度（磅）
            height: 图片高度（磅）
            caption: 图片标题
            
        返回:
            Dict: 包含图片信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")

            # 确定插入位置
            if position == "end":
                range_obj = self.current_doc.Range()
            elif position == "start":
                range_obj = self.current_doc.Range(0, 0)
            else:
                range_obj = self.current_doc.Range()

            # 添加图片标题
            if caption:
                range_obj.Text = f"{caption}\n"
                range_obj.Collapse(0)

            # 插入图片
            shape = self.current_doc.InlineShapes.AddPicture(image_path, False, True, range_obj)

            # 调整图片尺寸
            if width:
                shape.Width = width
            if height:
                shape.Height = height

            return {
                "success": True,
                "image_path": image_path,
                "width": shape.Width,
                "height": shape.Height
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def save_document(self, file_path: str, format_type: str = "docx") -> Dict[str, Any]:
        """保存文档，支持多种格式
        
        参数:
            file_path: 保存的文件路径
            format_type: 文件格式，支持：docx, doc, pdf, rtf, txt, html, xml
            
        返回:
            Dict: 包含保存结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 确保目录存在
            directory = os.path.dirname(file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)

            # 格式映射表
            format_map = {
                "docx": 16,  # wdFormatDocumentDefault
                "doc": 0,  # wdFormatDocument
                "pdf": 17,  # wdFormatPDF
                "rtf": 6,  # wdFormatRTF
                "txt": 7,  # wdFormatText
                "html": 8,  # wdFormatHTML
                "xml": 11  # wdFormatXML
            }

            format_code = format_map.get(format_type.lower(), 16)

            # 清理文件名中的非法字符
            safe_file_path = self._sanitize_filename(file_path)
            
            # 确保使用绝对路径
            if not os.path.isabs(safe_file_path):
                safe_file_path = os.path.abspath(safe_file_path)

            # 保存文档
            self.current_doc.SaveAs2(safe_file_path, format_code)

            return {
                "success": True,
                "file_path": safe_file_path,
                "format": format_type,
                "size": os.path.getsize(safe_file_path)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _sanitize_filename(self, filename):
        """清理文件名中的非法字符
        
        Windows系统中，文件名不能包含以下字符：\/:*?"<>|
        
        参数:
            filename: 原始文件名
            
        返回:
            str: 清理后的安全文件名
        """
        # 分离路径和文件名
        import os
        directory, basename = os.path.split(filename)

        # 只清理文件名部分，保留路径分隔符
        sanitized_basename = re.sub(r'[\'\"<>|?*]', '', basename)

        # 重新组合路径和文件名
        if directory:
            sanitized_filename = os.path.join(directory, sanitized_basename)
        else:
            sanitized_filename = sanitized_basename

        # 处理特殊情况，如文件名为空
        if not sanitized_filename:
            sanitized_filename = f"document_{uuid.uuid4().hex}.docx"

        return sanitized_filename

    def get_document_info(self) -> Dict[str, Any]:
        """获取详细文档信息
        
        返回:
            Dict: 包含文档统计信息和结构信息的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 获取文档统计信息
            word_count = self.current_doc.Words.Count
            char_count = self.current_doc.Characters.Count
            paragraph_count = self.current_doc.Paragraphs.Count
            page_count = self.current_doc.Range().Information(4)  # wdActiveEndPageNumber

            # 获取表格信息
            tables_info = []
            for i in range(1, self.current_doc.Tables.Count + 1):
                table = self.current_doc.Tables(i)
                tables_info.append({
                    "index": i,
                    "rows": table.Rows.Count,
                    "cols": table.Columns.Count,
                    "title": table.Title if hasattr(table, 'Title') else None
                })

            # 获取图片信息
            images_info = []
            for i in range(1, self.current_doc.InlineShapes.Count + 1):
                shape = self.current_doc.InlineShapes(i)
                images_info.append({
                    "index": i,
                    "type": shape.Type,
                    "width": shape.Width,
                    "height": shape.Height
                })

            return {
                "success": True,
                "document_name": self.current_doc.Name,
                "document_path": self.current_doc.Path,
                "statistics": {
                    "word_count": word_count,
                    "char_count": char_count,
                    "paragraph_count": paragraph_count,
                    "page_count": page_count,
                    "table_count": len(tables_info),
                    "image_count": len(images_info)
                },
                "tables": tables_info,
                "images": images_info
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def set_document_metadata(self, metadata: DocumentMetadata) -> Dict[str, Any]:
        """设置文档元数据
        
        参数:
            metadata: 文档元数据对象
            
        返回:
            Dict: 操作结果
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 设置各个元数据属性
            if metadata.title:
                self.current_doc.BuiltInDocumentProperties("Title").Value = metadata.title
            if metadata.subject:
                self.current_doc.BuiltInDocumentProperties("Subject").Value = metadata.subject
            if metadata.author:
                self.current_doc.BuiltInDocumentProperties("Author").Value = metadata.author
            if metadata.keywords:
                self.current_doc.BuiltInDocumentProperties("Keywords").Value = metadata.keywords
            if metadata.description:
                self.current_doc.BuiltInDocumentProperties("Comments").Value = metadata.description
            if metadata.category:
                self.current_doc.BuiltInDocumentProperties("Category").Value = metadata.category
            if metadata.company:
                self.current_doc.BuiltInDocumentProperties("Company").Value = metadata.company

            return {
                "success": True,
                "metadata": asdict(metadata)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_header_footer(self, header_text: Optional[str] = None,
                         footer_text: Optional[str] = None,
                         include_page_number: bool = False) -> Dict[str, Any]:
        """添加页眉页脚
        
        参数:
            header_text: 页眉文本
            footer_text: 页脚文本
            include_page_number: 是否包含页码
            
        返回:
            Dict: 操作结果
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 添加页眉
            if header_text:
                header_range = self.current_doc.Sections(1).Headers(1).Range
                header_range.Text = header_text
                if include_page_number:
                    header_range.Collapse(0)
                    header_range.Fields.Add(header_range, -1, "PAGE", True)  # 添加页码

            # 添加页脚
            if footer_text:
                footer_range = self.current_doc.Sections(1).Footers(1).Range
                footer_range.Text = footer_text
                if include_page_number and not header_text:
                    footer_range.Collapse(0)
                    footer_range.Fields.Add(footer_range, -1, "PAGE", True)

            return {
                "success": True,
                "header": header_text,
                "footer": footer_text,
                "page_number": include_page_number
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def insert_table_of_contents(self, title: str = "目录") -> Dict[str, Any]:
        """插入目录
        
        基于文档中的标题样式自动生成目录
        
        参数:
            title: 目录标题
            
        返回:
            Dict: 操作结果
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            # 在文档开头插入目录
            range_obj = self.current_doc.Range(0, 0)
            range_obj.Text = f"{title}\n"

            # 插入目录字段
            toc_range = self.current_doc.Range(range_obj.End, range_obj.End)
            self.current_doc.TablesOfContents.Add(toc_range)

            return {"success": True, "title": title}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def export_content(self, export_type: str = "text") -> Dict[str, Any]:
        """导出文档内容
        
        参数:
            export_type: 导出类型，支持：text, html
            
        返回:
            Dict: 包含导出内容的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")

            if export_type == "text":
                # 导出纯文本
                content = self.current_doc.Content.Text
            elif export_type == "html":
                # 导出为HTML格式
                temp_path = os.path.join(os.environ.get("TEMP", "C:\\temp"), f"temp_{uuid.uuid4().hex}.html")
                self.current_doc.SaveAs2(temp_path, 8)  # wdFormatHTML
                with open(temp_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                os.remove(temp_path)
            else:
                content = self.current_doc.Content.Text

            return {
                "success": True,
                "type": export_type,
                "content": content,
                "length": len(content)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _apply_style_to_range(self, range_obj, style: TextStyle):
        """应用样式到文本范围 - 增强版
        
        内部方法，用于将样式应用到指定的文本范围，支持丰富的格式选项
        
        参数:
            range_obj: Word Range对象
            style: 文本样式配置
        """
        # 设置字体
        if style.font_name:
            range_obj.Font.Name = style.font_name
        if style.font_size:
            range_obj.Font.Size = style.font_size
        
        # 设置字形
        if style.bold is not None:
            range_obj.Font.Bold = style.bold
        if style.italic is not None:
            range_obj.Font.Italic = style.italic
        if style.underline:
            range_obj.Font.Underline = 1  # wdUnderlineSingle
        if style.strikethrough:
            range_obj.Font.StrikeThrough = True

        # 设置颜色
        if style.color:
            try:
                r, g, b = map(int, style.color.split(','))
                range_obj.Font.Color = (b << 16) | (g << 8) | r
            except:
                pass
        
        # 设置高亮颜色
        if style.highlight_color:
            try:
                r, g, b = map(int, style.highlight_color.split(','))
                # WPS中高亮色的设置
                range_obj.Font.HighlightColorIndex = self._rgb_to_highlight_index(r, g, b)
            except:
                pass

        # 设置字符间距
        if style.character_spacing != 0:
            range_obj.Font.Spacing = style.character_spacing

        # 设置垂直位置（上标/下标）
        position_map = {
            "normal": 0,        # wdBaselineAlignBaseline
            "superscript": 1,   # wdBaselineAlignSuperscript
            "subscript": 2      # wdBaselineAlignSubscript
        }
        if style.vertical_position in position_map:
            range_obj.Font.Superscript = (style.vertical_position == "superscript")
            range_obj.Font.Subscript = (style.vertical_position == "subscript")

        # 设置文字效果
        if style.text_effect:
            effect_map = {
                "shadow": lambda: setattr(range_obj.Font, 'Shadow', True),
                "outline": lambda: setattr(range_obj.Font, 'Outline', True),
                "emboss": lambda: setattr(range_obj.Font, 'Emboss', True),
                "engrave": lambda: setattr(range_obj.Font, 'Engrave', True)
            }
            if style.text_effect in effect_map:
                try:
                    effect_map[style.text_effect]()
                except:
                    pass

        # 设置对齐方式
        alignment_map = {
            "left": 0,      # wdAlignParagraphLeft
            "center": 1,    # wdAlignParagraphCenter
            "right": 2,     # wdAlignParagraphRight
            "justify": 3,   # wdAlignParagraphJustify
            "distribute": 4 # wdAlignParagraphDistribute
        }
        if style.alignment in alignment_map:
            range_obj.ParagraphFormat.Alignment = alignment_map[style.alignment]
    
    def _rgb_to_highlight_index(self, r: int, g: int, b: int) -> int:
        """将RGB颜色转换为WPS高亮色索引
        
        参数:
            r, g, b: RGB颜色值
            
        返回:
            int: WPS高亮色索引
        """
        # WPS常用高亮色映射
        highlight_colors = {
            (255, 255, 0): 7,   # 黄色
            (0, 255, 0): 4,     # 亮绿色
            (0, 255, 255): 3,   # 青色
            (255, 0, 255): 6,   # 洋红色
            (255, 0, 0): 6,     # 红色
            (0, 0, 255): 5,     # 蓝色
            (128, 128, 128): 2, # 灰色
            (255, 255, 255): 8, # 白色
        }
        
        # 找到最接近的颜色
        min_distance = float('inf')
        best_index = 7  # 默认黄色
        
        for (cr, cg, cb), index in highlight_colors.items():
            distance = ((r - cr) ** 2 + (g - cg) ** 2 + (b - cb) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                best_index = index
        
        return best_index

    def _apply_paragraph_format(self, range_obj, paragraph_format: ParagraphFormat):
        """应用段落格式 - 增强版
        
        内部方法，用于将段落格式应用到指定的文本范围，支持丰富的段落选项
        
        参数:
            range_obj: Word Range对象
            paragraph_format: 段落格式配置
        """
        pf = range_obj.ParagraphFormat
        
        # 基本间距设置
        if paragraph_format.line_spacing:
            pf.LineSpacing = paragraph_format.line_spacing * 12  # 转换为磅
        if paragraph_format.space_before:
            pf.SpaceBefore = paragraph_format.space_before
        if paragraph_format.space_after:
            pf.SpaceAfter = paragraph_format.space_after
        if paragraph_format.first_line_indent:
            pf.FirstLineIndent = paragraph_format.first_line_indent
        if paragraph_format.left_indent:
            pf.LeftIndent = paragraph_format.left_indent
        if paragraph_format.right_indent:
            pf.RightIndent = paragraph_format.right_indent
        
        # 对齐方式
        alignment_map = {
            "left": 0,      # wdAlignParagraphLeft
            "center": 1,    # wdAlignParagraphCenter
            "right": 2,     # wdAlignParagraphRight
            "justify": 3,   # wdAlignParagraphJustify
            "distribute": 4 # wdAlignParagraphDistribute
        }
        if paragraph_format.alignment in alignment_map:
            pf.Alignment = alignment_map[paragraph_format.alignment]
        
        # 大纲级别
        if paragraph_format.outline_level:
            pf.OutlineLevel = paragraph_format.outline_level
        
        # 分页控制
        if paragraph_format.keep_with_next:
            pf.KeepWithNext = True
        if paragraph_format.keep_lines_together:
            pf.KeepTogether = True
        if paragraph_format.page_break_before:
            pf.PageBreakBefore = True
        if paragraph_format.widow_control is not None:
            pf.WidowControl = paragraph_format.widow_control
        
        # 制表位设置
        if paragraph_format.tab_stops:
            for position, tab_type in paragraph_format.tab_stops:
                tab_type_map = {
                    "left": 0,      # wdAlignTabLeft
                    "center": 1,    # wdAlignTabCenter
                    "right": 2,     # wdAlignTabRight
                    "decimal": 3,   # wdAlignTabDecimal
                    "bar": 4        # wdAlignTabBar
                }
                if tab_type in tab_type_map:
                    try:
                        pf.TabStops.Add(position, tab_type_map[tab_type])
                    except:
                        pass
        
        # 边框设置
        if paragraph_format.border_style != "none":
            border_map = {
                "single": 1,    # wdLineStyleSingle
                "double": 6,    # wdLineStyleDouble
                "dotted": 2,    # wdLineStyleDot
                "dashed": 7     # wdLineStyleDashSmallGap
            }
            if paragraph_format.border_style in border_map:
                try:
                    border_style = border_map[paragraph_format.border_style]
                    pf.Borders(1).LineStyle = border_style  # 上边框
                    pf.Borders(2).LineStyle = border_style  # 左边框
                    pf.Borders(3).LineStyle = border_style  # 下边框
                    pf.Borders(4).LineStyle = border_style  # 右边框
                except:
                    pass
        
        # 底纹设置
        if paragraph_format.shading_color:
            try:
                r, g, b = map(int, paragraph_format.shading_color.split(','))
                color_value = (b << 16) | (g << 8) | r
                pf.Shading.BackgroundPatternColor = color_value
            except:
                pass

    def set_page_style(self, page_style: PageStyle) -> Dict[str, Any]:
        """设置页面样式
        
        参数:
            page_style: 页面样式配置对象
            
        返回:
            Dict: 包含操作结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")
            
            page_setup = self.current_doc.PageSetup
            
            # 设置页面大小
            page_size_map = {
                "A4": (595.3, 841.9),      # A4: 21.0 x 29.7 cm
                "A3": (841.9, 1190.6),     # A3: 29.7 x 42.0 cm
                "A5": (419.5, 595.3),      # A5: 14.8 x 21.0 cm
                "Letter": (612.0, 792.0),  # Letter: 8.5 x 11 inch
                "Legal": (612.0, 1008.0),  # Legal: 8.5 x 14 inch
            }
            
            if page_style.page_size == "custom" and page_style.custom_width and page_style.custom_height:
                # 自定义页面大小（厘米转磅）
                page_setup.PageWidth = page_style.custom_width * 28.35
                page_setup.PageHeight = page_style.custom_height * 28.35
            elif page_style.page_size in page_size_map:
                width, height = page_size_map[page_style.page_size]
                page_setup.PageWidth = width
                page_setup.PageHeight = height
            
            # 设置页面方向
            if page_style.orientation == "landscape":
                page_setup.Orientation = 1  # wdOrientLandscape
            else:
                page_setup.Orientation = 0  # wdOrientPortrait
            
            # 设置页边距（厘米转磅）
            page_setup.TopMargin = page_style.margin_top * 28.35
            page_setup.BottomMargin = page_style.margin_bottom * 28.35
            page_setup.LeftMargin = page_style.margin_left * 28.35
            page_setup.RightMargin = page_style.margin_right * 28.35
            
            # 设置页眉页脚距离
            page_setup.HeaderDistance = page_style.header_distance * 28.35
            page_setup.FooterDistance = page_style.footer_distance * 28.35
            
            # 设置装订线
            if page_style.gutter > 0:
                page_setup.Gutter = page_style.gutter * 28.35
            
            # 设置镜像页边距
            if page_style.mirror_margins:
                page_setup.MirrorMargins = True
            
            return {
                "success": True,
                "page_size": page_style.page_size,
                "orientation": page_style.orientation,
                "margins": {
                    "top": page_style.margin_top,
                    "bottom": page_style.margin_bottom,
                    "left": page_style.margin_left,
                    "right": page_style.margin_right
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def set_header_footer(self, header_footer_style: HeaderFooterStyle) -> Dict[str, Any]:
        """设置页眉页脚 - 增强版
        
        参数:
            header_footer_style: 页眉页脚样式配置对象
            
        返回:
            Dict: 包含操作结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")
            
            # 获取页眉页脚区域
            sections = self.current_doc.Sections
            for section in sections:
                # 设置首页不同
                if header_footer_style.first_page_different:
                    section.PageSetup.DifferentFirstPageHeaderFooter = True
                
                # 设置奇偶页不同
                if header_footer_style.odd_even_different:
                    section.PageSetup.OddAndEvenPagesHeaderFooter = True
                
                # 设置页眉
                if header_footer_style.header_text:
                    headers = section.Headers
                    for header in headers:
                        header_range = header.Range
                        header_range.Text = header_footer_style.header_text
                        
                        # 应用页眉样式
                        if header_footer_style.header_style:
                            self._apply_style_to_range(header_range, header_footer_style.header_style)
                        
                        # 设置页眉对齐
                        alignment_map = {"left": 0, "center": 1, "right": 2}
                        if header_footer_style.header_alignment in alignment_map:
                            header_range.ParagraphFormat.Alignment = alignment_map[header_footer_style.header_alignment]
                
                # 设置页脚
                if header_footer_style.footer_text:
                    footers = section.Footers
                    for footer in footers:
                        footer_range = footer.Range
                        footer_range.Text = header_footer_style.footer_text
                        
                        # 应用页脚样式
                        if header_footer_style.footer_style:
                            self._apply_style_to_range(footer_range, header_footer_style.footer_style)
                        
                        # 设置页脚对齐
                        alignment_map = {"left": 0, "center": 1, "right": 2}
                        if header_footer_style.footer_alignment in alignment_map:
                            footer_range.ParagraphFormat.Alignment = alignment_map[header_footer_style.footer_alignment]
                
                # 添加页码
                if header_footer_style.include_page_number:
                    self._add_page_number(section, header_footer_style)
                
                # 设置边框线
                if header_footer_style.border_line:
                    try:
                        section.Headers(1).Range.ParagraphFormat.Borders(3).LineStyle = 1  # 下边框
                    except:
                        pass
            
            return {
                "success": True,
                "header_text": header_footer_style.header_text,
                "footer_text": header_footer_style.footer_text,
                "include_page_number": header_footer_style.include_page_number
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _add_page_number(self, section, header_footer_style: HeaderFooterStyle):
        """添加页码到指定位置
        
        参数:
            section: 文档节对象
            header_footer_style: 页眉页脚样式配置
        """
        try:
            # 页码格式映射
            format_map = {
                "arabic": 0,        # wdPageNumberStyleArabic
                "roman_upper": 1,   # wdPageNumberStyleUppercaseRoman
                "roman_lower": 2,   # wdPageNumberStyleLowercaseRoman
                "alpha_upper": 3,   # wdPageNumberStyleUppercaseLetter
                "alpha_lower": 4    # wdPageNumberStyleLowercaseLetter
            }
            
            page_num_format = format_map.get(header_footer_style.page_number_format, 0)
            position = header_footer_style.page_number_position
            
            if "header" in position:
                header = section.Headers(1)
                if "left" in position:
                    header.Range.ParagraphFormat.Alignment = 0  # 左对齐
                elif "center" in position:
                    header.Range.ParagraphFormat.Alignment = 1  # 居中
                elif "right" in position:
                    header.Range.ParagraphFormat.Alignment = 2  # 右对齐
                
                header.PageNumbers.Add(1, page_num_format)
            
            elif "footer" in position:
                footer = section.Footers(1)
                if "left" in position:
                    footer.Range.ParagraphFormat.Alignment = 0  # 左对齐
                elif "center" in position:
                    footer.Range.ParagraphFormat.Alignment = 1  # 居中
                elif "right" in position:
                    footer.Range.ParagraphFormat.Alignment = 2  # 右对齐
                
                footer.PageNumbers.Add(1, page_num_format)
        
        except Exception as e:
            pass  # 忽略页码设置错误

    def apply_document_theme(self, theme: DocumentTheme) -> Dict[str, Any]:
        """应用文档主题
        
        参数:
            theme: 文档主题配置对象
            
        返回:
            Dict: 包含操作结果的字典
        """
        try:
            if not self.current_doc:
                raise Exception("没有打开的文档")
            
            # 设置默认字体
            self.current_doc.Styles("正文").Font.Name = theme.body_font
            
            # 设置标题样式
            if theme.heading_styles:
                for level, style in theme.heading_styles.items():
                    try:
                        heading_style_name = f"标题 {level}"
                        if heading_style_name in [s.NameLocal for s in self.current_doc.Styles]:
                            heading_style = self.current_doc.Styles(heading_style_name)
                            heading_style.Font.Name = theme.heading_font
                            heading_style.Font.Size = style.font_size
                            heading_style.Font.Bold = style.bold
                            
                            # 设置颜色
                            if style.color:
                                r, g, b = map(int, style.color.split(','))
                                heading_style.Font.Color = (b << 16) | (g << 8) | r
                    except:
                        pass
            
            # 应用预定义主题样式
            if theme.theme_name == "professional":
                self._apply_professional_theme()
            elif theme.theme_name == "modern":
                self._apply_modern_theme()
            elif theme.theme_name == "elegant":
                self._apply_elegant_theme()
            elif theme.theme_name == "academic":
                self._apply_academic_theme()
            elif theme.theme_name == "creative":
                self._apply_creative_theme()
            elif theme.theme_name == "minimal":
                self._apply_minimal_theme()
            
            return {
                "success": True,
                "theme_name": theme.theme_name,
                "primary_color": theme.primary_color,
                "heading_font": theme.heading_font,
                "body_font": theme.body_font
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _apply_professional_theme(self):
        """应用专业主题"""
        try:
            # 设置标题样式为深蓝色、加粗
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = (189 << 16) | (129 << 8) | 79  # 深蓝色
                    style.Font.Bold = True
                except:
                    pass
        except:
            pass

    def _apply_modern_theme(self):
        """应用现代主题"""
        try:
            # 设置标题样式为灰色系、简洁
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = (64 << 16) | (64 << 8) | 64  # 深灰色
                    style.Font.Name = "微软雅黑"
                except:
                    pass
        except:
            pass

    def _apply_elegant_theme(self):
        """应用优雅主题"""
        try:
            # 设置标题样式为深棕色、优雅字体
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = (96 << 16) | (80 << 8) | 64  # 深棕色
                    style.Font.Name = "方正小标宋简体"
                except:
                    pass
        except:
            pass

    def _apply_academic_theme(self):
        """应用学术主题"""
        try:
            # 设置学术风格：黑色、传统字体
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = 0  # 黑色
                    style.Font.Name = "Times New Roman"
                    style.Font.Bold = True
                except:
                    pass
        except:
            pass

    def _apply_creative_theme(self):
        """应用创意主题"""
        try:
            # 设置创意风格：多彩、现代字体
            colors = [
                (70 << 16) | (150 << 8) | 247,  # 橙色
                (79 << 16) | (129 << 8) | 189,  # 蓝色
                (155 << 16) | (187 << 8) | 89,  # 绿色
                (192 << 16) | (80 << 8) | 77,   # 红色
                (128 << 16) | (100 << 8) | 162, # 紫色
                (247 << 16) | (150 << 8) | 70   # 黄色
            ]
            
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = colors[(level - 1) % len(colors)]
                    style.Font.Name = "微软雅黑"
                    style.Font.Bold = True
                except:
                    pass
        except:
            pass

    def _apply_minimal_theme(self):
        """应用极简主题"""
        try:
            # 设置极简风格：黑色、简洁
            for level in range(1, 7):
                try:
                    style_name = f"标题 {level}"
                    style = self.current_doc.Styles(style_name)
                    style.Font.Color = 0  # 黑色
                    style.Font.Name = "微软雅黑"
                    style.Font.Bold = False
                    style.Font.Size = 12 + (6 - level) * 2  # 递减字号
                except:
                    pass
        except:
            pass


class WPSToolWrapper:
    """增强版WPS工具包装器，为Agent提供友好的API接口
    
    提供高级封装功能，简化常见任务的操作流程
    """

    @staticmethod
    def create_document_from_template(template_path: str, output_path: str,
                                    variables: Dict[str, str] = None) -> Dict[str, Any]:
        """从模板创建文档并替换变量
        
        适用于基于模板批量生成文档的场景
        
        参数:
            template_path: 模板文件路径
            output_path: 输出文件路径
            variables: 变量字典，用于替换模板中的占位符
            
        返回:
            Dict: 操作结果
        """
        try:
            with WPSDocumentTool() as tool:
                result = tool.create_document(template_path)
                if not result["success"]:
                    return result

                # 替换模板变量
                if variables:
                    for key, value in variables.items():
                        tool.replace_text(f"{{{{{key}}}}}", str(value))

                save_result = tool.save_document(output_path)
                return save_result
        except Exception as e:
            return {"success": False, "error": str(e)}

    @staticmethod
    def create_report(file_path: str, title: str, sections: List[Dict[str, Any]],
                     metadata: DocumentMetadata = None) -> Dict[str, Any]:
        """创建结构化报告
        
        快速创建包含多个部分的完整报告
        
        参数:
            file_path: 输出文件路径
            title: 报告标题
            sections: 报告内容部分列表
            metadata: 文档元数据
            
        返回:
            Dict: 操作结果
        """
        try:
            with WPSDocumentTool() as tool:
                tool.create_document()

                # 设置文档属性
                if metadata:
                    tool.set_document_metadata(metadata)

                # 添加标题
                title_style = TextStyle(font_size=18, bold=True, alignment="center")
                tool.add_text(title + "\n\n", style=title_style)

                # 添加各个部分
                for section in sections:
                    section_title = section.get("title", "")
                    section_content = section.get("content", "")
                    section_type = section.get("type", "text")

                    if section_title:
                        subtitle_style = TextStyle(font_size=14, bold=True)
                        tool.add_paragraph(section_title, style=subtitle_style)

                    if section_type == "text":
                        tool.add_paragraph(section_content)
                    elif section_type == "table":
                        table_data = TableData(**section_content)
                        tool.create_table(table_data)
                    elif section_type == "image":
                        tool.insert_image(section_content["path"],
                                        caption=section_content.get("caption"))

                return tool.save_document(file_path)
        except Exception as e:
            return {"success": False, "error": str(e)}

    @staticmethod
    def batch_process_documents(input_dir: str, output_dir: str,
                              operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量处理文档
        
        对目录中的所有Word文档执行相同的操作
        
        参数:
            input_dir: 输入目录路径
            output_dir: 输出目录路径
            operations: 操作列表
            
        返回:
            Dict: 批量处理结果
        """
        try:
            results = []
            os.makedirs(output_dir, exist_ok=True)

            for filename in os.listdir(input_dir):
                if filename.endswith(('.doc', '.docx')):
                    input_path = os.path.join(input_dir, filename)
                    output_filename = f"processed_{filename}"
                    output_path = os.path.join(output_dir, output_filename)

                    with WPSDocumentTool() as tool:
                        tool.open_document(input_path)

                        for operation in operations:
                            op_type = operation.get("type")
                            if op_type == "replace":
                                tool.replace_text(
                                    operation["old"],
                                    operation["new"],
                                    operation.get("case_sensitive", True)
                                )
                            elif op_type == "add_header":
                                tool.add_header_footer(
                                    header_text=operation.get("header"),
                                    include_page_number=operation.get("page_number", False)
                                )

                        result = tool.save_document(output_path)
                        results.append({
                            "file": filename,
                            "result": result
                        })

            return {
                "success": True,
                "processed_files": len(results),
                "results": results
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    @staticmethod
    def extract_document_data(file_path: str) -> Dict[str, Any]:
        """提取文档完整数据
        
        获取文档的所有信息，包括内容和元数据
        
        参数:
            file_path: 文档路径
            
        返回:
            Dict: 包含完整文档数据的字典
        """
        try:
            with WPSDocumentTool() as tool:
                open_result = tool.open_document(file_path)
                if not open_result["success"]:
                    return open_result

                info = tool.get_document_info()
                content = tool.export_content("text")

                return {
                    "success": True,
                    "file_path": file_path,
                    "document_info": info,
                    "content": content["content"]
                }
        except Exception as e:
            return {"success": False, "error": str(e)}


# Agent友好的工具函数
class AgentWPSInterface:
    """专为Agent设计的WPS接口类
    
    提供最简化的API接口，适合AI Agent直接调用
    """

    @staticmethod
    def analyze_and_modify(file_path: str, instructions: Dict[str, Any]) -> Dict[str, Any]:
        """智能分析和修改文档
        
        根据指令对文档进行智能修改
        
        参数:
            file_path: 文档路径
            instructions: 修改指令字典
            
        返回:
            Dict: 修改结果
        """
        try:
            with WPSDocumentTool() as tool:
                open_result = tool.open_document(file_path)
                if not open_result["success"]:
                    return open_result

                results = []

                # 执行替换操作
                if "replacements" in instructions:
                    for replacement in instructions["replacements"]:
                        result = tool.replace_text(
                            replacement["old"],
                            replacement["new"],
                            replacement.get("case_sensitive", True)
                        )
                        results.append({"type": "replace", "result": result})

                # 添加内容
                if "add_content" in instructions:
                    for content in instructions["add_content"]:
                        content_type = content.get("type", "text")
                        if content_type == "text":
                            result = tool.add_paragraph(
                                content["text"],
                                style=TextStyle(**content.get("style", {}))
                            )
                        elif content_type == "table":
                            result = tool.create_table(
                                TableData(**content["table"]),
                                caption=content.get("caption")
                            )
                        results.append({"type": "add", "result": result})

                # 保存修改
                save_result = tool.save_document(file_path)

                return {
                    "success": True,
                    "modifications": results,
                    "save_result": save_result
                }
        except Exception as e:
            return {"success": False, "error": str(e)}

    @staticmethod
    def generate_summary(file_path: str) -> Dict[str, Any]:
        """生成文档摘要
        
        快速获取文档的基本信息和内容预览
        
        参数:
            file_path: 文档路径
            
        返回:
            Dict: 文档摘要信息
        """
        try:
            with WPSDocumentTool() as tool:
                open_result = tool.open_document(file_path)
                if not open_result["success"]:
                    return open_result

                info = tool.get_document_info()
                content = tool.export_content("text")

                # 简单的文本摘要
                text = content["content"]
                lines = text.split('\n')
                non_empty_lines = [line.strip() for line in lines if line.strip()]

                summary = {
                    "file_name": os.path.basename(file_path),
                    "total_pages": info["statistics"]["page_count"],
                    "total_words": info["statistics"]["word_count"],
                    "total_tables": info["statistics"]["table_count"],
                    "total_images": info["statistics"]["image_count"],
                    "paragraphs": len(non_empty_lines),
                    "preview": '\n'.join(non_empty_lines[:5]) if non_empty_lines else ""
                }

                return {
                    "success": True,
                    "summary": summary
                }
        except Exception as e:
            return {"success": False, "error": str(e)}


# ==================== Agent增强功能模块 ====================

from functools import wraps
import logging
from enum import Enum
from typing import Callable, Any


class OperationType(Enum):
    """操作类型枚举"""
    CREATE = "create"
    OPEN = "open"
    SAVE = "save"
    ADD_TEXT = "add_text"
    ADD_TABLE = "add_table"
    ADD_IMAGE = "add_image"
    REPLACE_TEXT = "replace_text"
    FORMAT_TEXT = "format_text"
    SET_METADATA = "set_metadata"
    EXPORT = "export"
    BATCH_PROCESS = "batch_process"


@dataclass
class OperationLog:
    """操作日志记录"""
    timestamp: datetime
    operation_type: OperationType
    parameters: Dict[str, Any]
    result: Dict[str, Any]
    execution_time: float
    agent_context: Optional[str] = None


class AgentFunctionRegistry:
    """Agent函数注册器
    
    为Agent提供函数发现和调用能力
    """
    
    def __init__(self):
        self.functions = {}
        self.operation_history = []
        self.logger = logging.getLogger(__name__)
    
    def register_function(self, name: str, func: Callable, description: str, 
                         parameters: Dict[str, Any], examples: List[str] = None):
        """注册Agent可调用的函数
        
        参数:
            name: 函数名称
            func: 函数对象
            description: 函数描述
            parameters: 参数说明
            examples: 使用示例
        """
        self.functions[name] = {
            "function": func,
            "description": description,
            "parameters": parameters,
            "examples": examples or [],
            "call_count": 0,
            "last_called": None
        }
    
    def get_function_schema(self) -> Dict[str, Any]:
        """获取所有函数的JSON Schema格式描述
        
        用于Agent理解可用功能
        """
        schema = {
            "functions": [],
            "operation_types": [op.value for op in OperationType]
        }
        
        for name, info in self.functions.items():
            schema["functions"].append({
                "name": name,
                "description": info["description"],
                "parameters": info["parameters"],
                "examples": info["examples"],
                "usage_stats": {
                    "call_count": info["call_count"],
                    "last_called": info["last_called"]
                }
            })
        
        return schema
    
    def execute_function(self, name: str, **kwargs) -> Dict[str, Any]:
        """执行注册的函数并记录日志"""
        if name not in self.functions:
            return {"success": False, "error": f"函数 {name} 未注册"}
        
        start_time = datetime.now()
        try:
            # 执行函数
            result = self.functions[name]["function"](**kwargs)
            
            # 更新统计信息
            self.functions[name]["call_count"] += 1
            self.functions[name]["last_called"] = start_time
            
            # 记录操作日志
            execution_time = (datetime.now() - start_time).total_seconds()
            log = OperationLog(
                timestamp=start_time,
                operation_type=OperationType(name.split('_')[0].lower()) if name.split('_')[0].lower() in [op.value for op in OperationType] else OperationType.CREATE,
                parameters=kwargs,
                result=result,
                execution_time=execution_time
            )
            self.operation_history.append(log)
            
            return result
        except Exception as e:
            error_result = {"success": False, "error": str(e)}
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 记录错误日志
            log = OperationLog(
                timestamp=start_time,
                operation_type=OperationType.CREATE,
                parameters=kwargs,
                result=error_result,
                execution_time=execution_time
            )
            self.operation_history.append(log)
            
            return error_result
    
    def get_operation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取操作历史记录"""
        recent_ops = self.operation_history[-limit:]
        return [asdict(op) for op in recent_ops]


class SmartWPSAgent:
    """智能WPS Agent
    
    提供自然语言理解和智能调用能力
    """
    
    def __init__(self, config_path: str = "config_deepseek.json"):
        self.registry = AgentFunctionRegistry()
        self.config = self._load_config(config_path)
        self._register_all_functions()
        self.context = {}
        self._current_tool = None  # 维护当前的WPS工具实例
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return {}
    
    def _register_all_functions(self):
        """注册所有WPS操作函数"""
        
        # 文档创建函数
        self.registry.register_function(
            "create_document",
            self._create_document_wrapper,
            "创建新的WPS文档",
            {
                "template_path": {"type": "string", "description": "模板文件路径（可选）", "required": False},
                "title": {"type": "string", "description": "文档标题（可选）", "required": False}
            },
            ["创建一个新文档", "基于模板创建文档"]
        )
        
        # 文本添加函数
        self.registry.register_function(
            "add_text",
            self._add_text_wrapper,
            "向文档添加文本内容",
            {
                "text": {"type": "string", "description": "要添加的文本内容", "required": True},
                "position": {"type": "string", "description": "插入位置: start/end", "required": False, "default": "end"},
                "font_size": {"type": "integer", "description": "字体大小", "required": False, "default": 12},
                "bold": {"type": "boolean", "description": "是否加粗", "required": False, "default": False},
                "alignment": {"type": "string", "description": "对齐方式: left/center/right", "required": False, "default": "left"}
            },
            ["添加一段文字", "在文档开头插入标题"]
        )
        
        # 表格创建函数
        self.registry.register_function(
            "create_table",
            self._create_table_wrapper,
            "创建表格",
            {
                "rows": {"type": "integer", "description": "表格行数", "required": True},
                "cols": {"type": "integer", "description": "表格列数", "required": True},
                "data": {"type": "array", "description": "表格数据（二维数组）", "required": False},
                "caption": {"type": "string", "description": "表格标题", "required": False},
                "style": {"type": "string", "description": "表格样式", "required": False}
            },
            ["创建一个3行4列的表格", "创建带标题的数据表"]
        )
        
        # 文档保存函数
        self.registry.register_function(
            "save_document",
            self._save_document_wrapper,
            "保存文档",
            {
                "file_path": {"type": "string", "description": "保存路径", "required": True},
                "format": {"type": "string", "description": "文件格式: docx/pdf/txt", "required": False, "default": "docx"}
            },
            ["保存文档为word格式", "导出为PDF文件"]
        )
        
        # 智能分析函数
        self.registry.register_function(
            "analyze_document",
            self._analyze_document_wrapper,
            "分析文档内容和结构",
            {
                "file_path": {"type": "string", "description": "文档路径", "required": True},
                "analysis_type": {"type": "string", "description": "分析类型: summary/structure/content", "required": False, "default": "summary"}
            },
            ["分析文档内容", "获取文档摘要"]
        )
    
    def _create_document_wrapper(self, template_path: str = None, title: str = None) -> Dict[str, Any]:
        """文档创建包装器"""
        try:
            # 如果已有工具实例，先关闭
            if self._current_tool:
                self._current_tool.disconnect()
            
            # 创建新的工具实例并保持连接
            self._current_tool = WPSDocumentTool()
            self._current_tool.connect()
            
            result = self._current_tool.create_document(template_path)
            if result["success"]:
                self.context["current_document"] = result["document_name"]
                self.context["document_path"] = result["document_path"]
                
                if title:
                    # 添加标题
                    title_style = TextStyle(font_size=18, bold=True, alignment="center")
                    self._current_tool.add_text(f"{title}\n\n", style=title_style)
                    self.context["has_title"] = True
                    
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _add_text_wrapper(self, text: str, position: str = "end", 
                         font_size: int = 12, bold: bool = False, 
                         alignment: str = "left") -> Dict[str, Any]:
        """文本添加包装器"""
        try:
            # 检查是否有活动的文档
            if not self._current_tool or not self._current_tool.current_doc:
                return {"success": False, "error": "没有打开的文档，请先创建或打开文档"}
            
            # 创建样式
            style = TextStyle(
                font_size=font_size,
                bold=bold,
                alignment=alignment
            )
            
            return self._current_tool.add_text(text, position, style)
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _create_table_wrapper(self, rows: int, cols: int, data: List[List[str]] = None,
                             caption: str = None, style: str = None) -> Dict[str, Any]:
        """表格创建包装器"""
        try:
            # 检查是否有活动的文档
            if not self._current_tool or not self._current_tool.current_doc:
                return {"success": False, "error": "没有打开的文档，请先创建或打开文档"}
            
            # 如果没有提供数据，创建空表格
            if not data:
                data = [["" for _ in range(cols)] for _ in range(rows)]
            
            table_data = TableData(
                rows=rows,
                cols=cols,
                data=data,
                style=style
            )
            
            return self._current_tool.create_table(table_data, caption=caption)
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _save_document_wrapper(self, file_path: str, format: str = "docx") -> Dict[str, Any]:
        """文档保存包装器"""
        try:
            # 检查是否有活动的文档
            if not self._current_tool or not self._current_tool.current_doc:
                return {"success": False, "error": "没有打开的文档，请先创建或打开文档"}
            
            result = self._current_tool.save_document(file_path, format)
            if result["success"]:
                self.context["last_saved_path"] = result["file_path"]
            return result
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _analyze_document_wrapper(self, file_path: str, analysis_type: str = "summary") -> Dict[str, Any]:
        """文档分析包装器"""
        try:
            if analysis_type == "summary":
                return AgentWPSInterface.generate_summary(file_path)
            elif analysis_type == "content":
                return WPSToolWrapper.extract_document_data(file_path)
            else:
                with WPSDocumentTool() as tool:
                    tool.open_document(file_path)
                    return tool.get_document_info()
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def process_natural_language(self, instruction: str) -> Dict[str, Any]:
        """处理自然语言指令
        
        将自然语言转换为函数调用
        """
        instruction = instruction.lower().strip()
        
        # 简单的规则匹配（实际项目中应使用AI模型）
        if "创建" in instruction and "文档" in instruction:
            title = None
            if "标题" in instruction:
                # 提取标题
                title_match = re.search(r'标题.*?[是为][\s]*[\'"]?([^\'"\n]+)[\'"]?', instruction)
                if title_match:
                    title = title_match.group(1).strip()
            
            return self.registry.execute_function("create_document", title=title)
        
        elif "添加" in instruction and ("文字" in instruction or "文本" in instruction):
            # 提取要添加的文本
            text_match = re.search(r'[添加写入][\s]*[\'"]?([^\'"\n]+)[\'"]?', instruction)
            text = text_match.group(1) if text_match else "默认文本"
            
            # 检查格式要求
            bold = "加粗" in instruction or "粗体" in instruction
            font_size = 12
            size_match = re.search(r'(\d+)[\s]*[号磅点]', instruction)
            if size_match:
                font_size = int(size_match.group(1))
            
            alignment = "left"
            if "居中" in instruction:
                alignment = "center"
            elif "右对齐" in instruction:
                alignment = "right"
            
            return self.registry.execute_function(
                "add_text", 
                text=text, 
                bold=bold, 
                font_size=font_size,
                alignment=alignment
            )
        
        elif "创建" in instruction and "表格" in instruction:
            # 提取表格尺寸
            size_match = re.search(r'(\d+)[\s]*[行×x][\s]*(\d+)[\s]*列', instruction)
            if size_match:
                rows, cols = int(size_match.group(1)), int(size_match.group(2))
            else:
                rows, cols = 3, 3  # 默认尺寸
            
            return self.registry.execute_function("create_table", rows=rows, cols=cols)
        
        elif "保存" in instruction:
            # 提取文件路径和格式
            # 匹配模式：保存文档到'文件名' 或 保存到文件名
            path_match = re.search(r'(?:保存.*?到|保存)\s*[\'"]?([^\'"\n,，]+?\.?\w*)[\'"]?(?:\s|$)', instruction)
            if path_match:
                file_path = path_match.group(1).strip()
                # 如果没有扩展名，添加默认扩展名
                if not file_path.endswith(('.docx', '.doc', '.pdf', '.txt')):
                    file_path += '.docx'
            else:
                file_path = "document.docx"
            
            format_type = "docx"
            if "pdf" in instruction.lower():
                format_type = "pdf"
            elif "txt" in instruction or "文本" in instruction:
                format_type = "txt"
            
            return self.registry.execute_function("save_document", file_path=file_path, format=format_type)
        
        elif "分析" in instruction:
            try:
                path_match = re.search(r'[分析查看][\s]*[\'"]?([^\'"\n]+)[\'"]?', instruction)
                file_path = path_match.group(1) if path_match else ""
                
                if not file_path:
                    return {"success": False, "error": "请指定要分析的文件路径"}
                
                return self.registry.execute_function("analyze_document", file_path=file_path)
            except Exception as e:
                return {"success": False, "error": f"分析指令解析错误: {str(e)}"}
        
        else:
            return {
                "success": False, 
                "error": f"无法理解指令: {instruction}",
                "suggestion": "请使用更明确的指令，如：'创建一个新文档'、'添加文字'、'创建表格'等"
            }
    
    def get_available_functions(self) -> Dict[str, Any]:
        """获取可用函数列表"""
        return self.registry.get_function_schema()
    
    def get_context(self) -> Dict[str, Any]:
        """获取当前上下文信息"""
        return {
            "context": self.context,
            "operation_history": self.registry.get_operation_history(),
            "available_functions": list(self.registry.functions.keys()),
            "has_active_document": self._current_tool is not None and self._current_tool.current_doc is not None
        }
    
    def close_document(self) -> Dict[str, Any]:
        """关闭当前文档并清理资源"""
        try:
            if self._current_tool:
                self._current_tool.disconnect()
                self._current_tool = None
                self.context.clear()
                return {"success": True, "message": "文档已关闭，资源已清理"}
            else:
                return {"success": True, "message": "没有打开的文档"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def __del__(self):
        """析构函数，确保资源清理"""
        if hasattr(self, '_current_tool') and self._current_tool:
            try:
                self._current_tool.disconnect()
            except:
                pass


"""
使用示例：

# 创建智能Agent
agent = SmartWPSAgent()

# 自然语言调用
result = agent.process_natural_language("创建一个标题为'月度报告'的新文档")
result = agent.process_natural_language("添加文字'这是报告内容'，字体加粗，16号字")
result = agent.process_natural_language("创建一个5行3列的表格")
result = agent.process_natural_language("保存文档到'report.docx'")

# 获取可用功能
functions = agent.get_available_functions()

# 查看操作历史
history = agent.get_context()
"""
