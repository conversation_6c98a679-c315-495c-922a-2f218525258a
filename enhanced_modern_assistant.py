# -*- coding: utf-8 -*-
"""
增强版现代化智能办公助手 - 集成所有UI优化
包含现代设计系统、交互增强、无障碍功能等
"""

import sys
import json
import os
import threading
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTextEdit, QLineEdit, QPushButton, QLabel, QSplitter, QFrame,
    QComboBox, QCheckBox, QProgressBar, QTabWidget, QGroupBox,
    QScrollArea, QListWidget, QListWidgetItem, QMessageBox,
    QFileDialog, QStatusBar, QToolBar, QSpacerItem, QSizePolicy,
    QGridLayout, QStackedWidget, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QPropertyAnimation, 
    QEasingCurve, QRect, pyqtSlot, QParallelAnimationGroup
)
from PyQt6.QtGui import (
    QFont, QIcon, QPixmap, QPalette, QColor, QAction,
    QTextCharFormat, QTextCursor, QSyntaxHighlighter, QTextDocument,
    QLinearGradient, QPainter, QPen, QBrush, QFontMetrics
)

# 导入增强模块
from ui_design_system import ModernDesignSystem, ModernStyleSheets
from enhanced_ui_components import (
    EnhancedModernCard, EnhancedChatMessage, EnhancedNavigationButton,
    EnhancedProgressIndicator, EnhancedStatusBar
)
from interactive_enhancements import (
    AnimationManager, InteractiveButton, LoadingIndicator, NotificationToast
)
from accessibility_enhancements import (
    AccessibilityManager, AccessibleWidget, AccessibleButton, 
    AccessibleLineEdit, KeyboardNavigationHelper
)

# 尝试导入项目模块
try:
    from intelligent_agent_v2 import IntelligentDocumentAgentV2
    from enhanced_prompts_config import EnhancedPromptsConfig
    AGENT_AVAILABLE = True
except ImportError:
    AGENT_AVAILABLE = False
    print("警告：未找到智能代理模块，将使用演示模式")


class EnhancedModernOfficeAssistant(QMainWindow):
    """增强版现代化智能办公助手"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.agent = None
        self.current_session = None
        self.animation_manager = AnimationManager()
        self.accessibility_manager = None
        
        # UI组件
        self.nav_buttons = {}
        self.content_stack = None
        self.chat_layout = None
        self.chat_scroll = None
        self.chat_input = None
        self.status_bar = None
        
        # 初始化
        self.init_agent()
        self.setup_ui()
        self.setup_accessibility()
        self.setup_animations()
        
        # 显示欢迎动画
        self.show_welcome_animation()
    
    def init_agent(self):
        """初始化AI代理"""
        if AGENT_AVAILABLE:
            try:
                config_path = Path("config_deepseek.json")
                if config_path.exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.agent = IntelligentDocumentAgentV2(
                        api_key=config.get('api_key', ''),
                        base_url=config.get('base_url', ''),
                        model=config.get('model', 'deepseek-chat')
                    )
                    
                    # 启动新会话
                    self.current_session = self.agent.start_conversation("office_user")
                    print("✅ AI代理初始化成功")
                else:
                    print("⚠️ 配置文件不存在，使用演示模式")
            except Exception as e:
                print(f"❌ AI代理初始化失败: {e}")
                self.agent = None
    
    def setup_ui(self):
        """设置现代化用户界面"""
        self.setWindowTitle("🚀 智能办公助手 - Enhanced AI Office Assistant")
        self.setGeometry(100, 100, 1500, 1000)  # 增加窗口大小
        self.setMinimumSize(1200, 800)  # 设置最小尺寸
        
        # 应用现代化样式
        self.apply_modern_styles()
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        central_widget.setLayout(main_layout)
        
        # 左侧导航栏
        left_nav = self.create_enhanced_navigation_panel()
        main_layout.addWidget(left_nav)
        
        # 右侧主内容区
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)
        
        # 创建各个功能页面
        self.create_enhanced_function_pages()
        
        # 设置比例
        main_layout.setStretch(0, 0)  # 导航栏固定宽度
        main_layout.setStretch(1, 1)  # 内容区占满剩余空间
        
        # 创建增强状态栏
        self.setup_enhanced_status_bar()
    
    def apply_modern_styles(self):
        """应用现代化样式"""
        combined_styles = (
            ModernStyleSheets.get_main_window_style() +
            ModernStyleSheets.get_button_style() +
            ModernStyleSheets.get_input_style() +
            ModernStyleSheets.get_scrollbar_style() +
            ModernStyleSheets.get_group_box_style() +
            ModernStyleSheets.get_combo_box_style() +
            ModernStyleSheets.get_navigation_style() +
            ModernStyleSheets.get_chat_bubble_style()
        )
        
        # 添加全局动画支持
        combined_styles += """
            * {
                transition: all 0.3s ease;
            }
        """
        
        self.setStyleSheet(combined_styles)
    
    def create_enhanced_navigation_panel(self) -> QWidget:
        """创建增强版导航面板"""
        nav_panel = QWidget()
        nav_panel.setFixedWidth(320)  # 增加宽度以容纳更多内容
        nav_panel.setObjectName("NavigationPanel")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg')
        )
        layout.setSpacing(ModernDesignSystem.get_spacing('md'))
        nav_panel.setLayout(layout)
        
        # 顶部品牌区域 - 增强版
        brand_frame = self.create_brand_section()
        layout.addWidget(brand_frame)
        
        # 快速操作区域
        quick_actions = self.create_quick_actions_section()
        layout.addWidget(quick_actions)
        
        # 导航按钮组
        nav_section = self.create_navigation_buttons_section()
        layout.addWidget(nav_section)
        
        layout.addStretch()
        
        # 底部信息区域
        bottom_section = self.create_bottom_info_section()
        layout.addWidget(bottom_section)
        
        return nav_panel
    
    def create_brand_section(self) -> QWidget:
        """创建品牌区域"""
        brand_frame = QFrame()
        brand_frame.setFixedHeight(100)
        brand_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('primary.main')}, 
                    stop:1 {ModernDesignSystem.get_color('primary.light')});
                border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                padding: {ModernDesignSystem.get_spacing('lg')}px;
            }}
        """)
        
        # 添加阴影效果
        shadow_effect = ModernDesignSystem.create_shadow_effect('lg')
        brand_frame.setGraphicsEffect(shadow_effect)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('md')
        )
        brand_frame.setLayout(layout)
        
        # 标题
        title_layout = QHBoxLayout()
        
        logo_label = QLabel("🚀")
        logo_label.setFont(ModernDesignSystem.get_font('3xl', 'normal'))
        logo_label.setStyleSheet("color: white;")
        title_layout.addWidget(logo_label)
        
        title_text = QLabel("智能办公助手")
        title_text.setFont(ModernDesignSystem.get_font('xl', 'bold'))
        title_text.setStyleSheet("color: white;")
        title_layout.addWidget(title_text)
        
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # 副标题
        subtitle = QLabel("Enhanced AI Office Assistant")
        subtitle.setFont(ModernDesignSystem.get_font('sm', 'normal'))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.8);")
        layout.addWidget(subtitle)
        
        return brand_frame
    
    def create_quick_actions_section(self) -> QWidget:
        """创建快速操作区域"""
        actions_frame = QFrame()
        actions_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_50')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md')
        )
        actions_frame.setLayout(layout)
        
        # 标题
        title = QLabel("快速操作")
        title.setFont(ModernDesignSystem.get_font('base', 'semibold'))
        title.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_700')};")
        layout.addWidget(title)
        
        # 快速按钮
        buttons_layout = QHBoxLayout()
        
        new_btn = InteractiveButton("新建", "primary")
        new_btn.clicked.connect(self.new_chat)
        new_btn.setAccessibleName("新建对话")
        buttons_layout.addWidget(new_btn)
        
        save_btn = InteractiveButton("保存", "secondary")
        save_btn.clicked.connect(self.save_current)
        save_btn.setAccessibleName("保存当前内容")
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
        
        return actions_frame
    
    def create_navigation_buttons_section(self) -> QWidget:
        """创建导航按钮区域"""
        nav_frame = QFrame()
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(ModernDesignSystem.get_spacing('xs'))
        nav_frame.setLayout(layout)
        
        # 导航标题
        nav_title = QLabel("功能导航")
        nav_title.setFont(ModernDesignSystem.get_font('base', 'semibold'))
        nav_title.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_700')}; margin-bottom: {ModernDesignSystem.get_spacing('sm')}px;")
        layout.addWidget(nav_title)
        
        # 导航按钮
        nav_items = [
            ("chat", "💬", "智能对话", "与AI助手自由对话交流"),
            ("document", "📄", "文档助手", "创建各种专业文档"),
            ("data", "📊", "数据分析", "数据处理和可视化分析"),
            ("email", "📧", "邮件助手", "邮件撰写和管理工具"),
            ("schedule", "📅", "日程管理", "智能日程规划助手"),
            ("translate", "🌐", "翻译工具", "多语言翻译服务"),
            ("summary", "📋", "内容总结", "文档和网页智能总结"),
            ("code", "💻", "代码助手", "编程辅助和代码生成")
        ]
        
        # 创建按钮组用于单选
        self.nav_button_group = QButtonGroup()
        
        for func_id, icon, title, desc in nav_items:
            btn = EnhancedNavigationButton(func_id, icon, title, desc)
            btn.clicked.connect(lambda checked, fid=func_id: self.switch_page(fid))
            btn.setAccessibleName(f"切换到{title}")
            btn.setAccessibleDescription(desc)
            
            self.nav_buttons[func_id] = btn
            self.nav_button_group.addButton(btn)
            layout.addWidget(btn)
        
        # 默认选中对话
        self.nav_buttons["chat"].setChecked(True)
        
        return nav_frame
    
    def create_bottom_info_section(self) -> QWidget:
        """创建底部信息区域"""
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_50')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('md')
        )
        bottom_frame.setLayout(layout)
        
        # 连接状态
        status_layout = QHBoxLayout()
        
        status_indicator = QLabel("●")
        status_indicator.setFont(ModernDesignSystem.get_font('sm', 'bold'))
        if self.agent:
            status_indicator.setStyleSheet(f"color: {ModernDesignSystem.get_color('success.main')};")
            status_text = "AI已连接"
        else:
            status_indicator.setStyleSheet(f"color: {ModernDesignSystem.get_color('error.main')};")
            status_text = "演示模式"
        
        status_layout.addWidget(status_indicator)
        
        status_label = QLabel(status_text)
        status_label.setFont(ModernDesignSystem.get_font('sm', 'normal'))
        status_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
        status_layout.addWidget(status_label)
        
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        # 设置按钮
        settings_btn = AccessibleButton("⚙️ 设置")
        settings_btn.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                text-align: left;
                padding: {ModernDesignSystem.get_spacing('sm')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_600')};
                border-radius: {ModernDesignSystem.get_border_radius('sm')}px;
            }}
            QPushButton:hover {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_200')};
            }}
        """)
        settings_btn.clicked.connect(self.show_settings)
        layout.addWidget(settings_btn)
        
        # 帮助按钮
        help_btn = AccessibleButton("❓ 帮助")
        help_btn.setStyleSheet(settings_btn.styleSheet())
        help_btn.clicked.connect(self.show_help)
        layout.addWidget(help_btn)
        
        return bottom_frame

    def create_enhanced_function_pages(self):
        """创建增强版功能页面"""
        # 智能对话页面
        self.chat_page = self.create_enhanced_chat_page()
        self.content_stack.addWidget(self.chat_page)

        # 文档助手页面
        self.document_page = self.create_enhanced_document_page()
        self.content_stack.addWidget(self.document_page)

        # 其他页面使用占位符
        for page_name in ["data", "email", "schedule", "translate", "summary", "code"]:
            placeholder_page = self.create_placeholder_page(page_name)
            self.content_stack.addWidget(placeholder_page)

    def create_enhanced_chat_page(self) -> QWidget:
        """创建增强版聊天页面"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('xl'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('xl'),
            ModernDesignSystem.get_spacing('lg')
        )
        layout.setSpacing(ModernDesignSystem.get_spacing('lg'))
        page.setLayout(layout)

        # 页面标题
        title_layout = QHBoxLayout()

        title_label = QLabel("💬 智能对话")
        title_label.setFont(ModernDesignSystem.get_font('2xl', 'bold'))
        title_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_800')};")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 新建对话按钮
        new_chat_btn = InteractiveButton("新建对话", "secondary")
        new_chat_btn.clicked.connect(self.new_chat)
        new_chat_btn.setAccessibleName("新建对话")
        title_layout.addWidget(new_chat_btn)

        layout.addLayout(title_layout)

        # 聊天区域
        self.chat_scroll = QScrollArea()
        self.chat_scroll.setWidgetResizable(True)
        self.chat_scroll.setStyleSheet(f"""
            QScrollArea {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
            }}
        """)

        # 聊天内容容器
        chat_widget = QWidget()
        self.chat_layout = QVBoxLayout()
        self.chat_layout.setContentsMargins(
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg')
        )
        self.chat_layout.setSpacing(ModernDesignSystem.get_spacing('md'))
        chat_widget.setLayout(self.chat_layout)
        self.chat_scroll.setWidget(chat_widget)

        layout.addWidget(self.chat_scroll, 1)

        # 输入区域
        input_frame = QFrame()
        input_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                padding: {ModernDesignSystem.get_spacing('lg')}px;
            }}
        """)

        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(ModernDesignSystem.get_spacing('md'))
        input_frame.setLayout(input_layout)

        # 输入框
        self.chat_input = AccessibleLineEdit("请输入您的问题或需求...")
        self.chat_input.setFont(ModernDesignSystem.get_font('base', 'normal'))
        self.chat_input.setMinimumHeight(50)
        self.chat_input.returnPressed.connect(self.send_chat_message)
        self.chat_input.setAccessibleName("聊天输入框")
        self.chat_input.setAccessibleDescription("在此输入您想要与AI助手交流的内容")
        input_layout.addWidget(self.chat_input, 1)

        # 发送按钮
        send_btn = InteractiveButton("发送 🚀", "primary")
        send_btn.setMinimumHeight(50)
        send_btn.setFixedWidth(120)
        send_btn.clicked.connect(self.send_chat_message)
        send_btn.setAccessibleName("发送消息")
        input_layout.addWidget(send_btn)

        layout.addWidget(input_frame)

        # 添加欢迎消息
        self.add_welcome_message()

        return page

    def create_enhanced_document_page(self) -> QWidget:
        """创建增强版文档页面"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('xl'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('xl'),
            ModernDesignSystem.get_spacing('lg')
        )
        layout.setSpacing(ModernDesignSystem.get_spacing('lg'))
        page.setLayout(layout)

        # 页面标题
        title_label = QLabel("📄 文档助手")
        title_label.setFont(ModernDesignSystem.get_font('2xl', 'bold'))
        title_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_800')};")
        layout.addWidget(title_label)

        # 描述
        desc_label = QLabel("选择下方模板快速创建专业文档，或描述您的需求让AI为您定制")
        desc_label.setFont(ModernDesignSystem.get_font('base', 'normal'))
        desc_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 文档模板卡片
        cards_frame = QFrame()
        cards_layout = QGridLayout()
        cards_layout.setSpacing(ModernDesignSystem.get_spacing('lg'))
        cards_frame.setLayout(cards_layout)

        doc_cards = [
            ("📊 商务报告", "创建专业的商务报告和分析文档", "立即创建"),
            ("📚 技术文档", "生成详细的技术文档和API文档", "开始编写"),
            ("🎓 学术论文", "撰写规范的学术论文和研究报告", "开始撰写"),
            ("📖 用户手册", "制作清晰的产品使用手册", "创建手册"),
            ("📋 项目计划", "制定详细的项目计划和时间表", "制定计划"),
            ("📝 会议纪要", "整理和格式化会议记录", "整理纪要"),
        ]

        for i, (title, desc, action) in enumerate(doc_cards):
            card = EnhancedModernCard(title, "", desc, action)
            card.clicked.connect(lambda t=title: self.create_document(t))
            card.setAccessibleName(f"文档模板: {title}")
            card.setAccessibleDescription(f"{desc}，点击{action}")
            cards_layout.addWidget(card, i // 3, i % 3)

        layout.addWidget(cards_frame)
        layout.addStretch()

        return page

    def create_placeholder_page(self, page_name: str) -> QWidget:
        """创建占位符页面"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        page.setLayout(layout)

        # 图标和标题映射
        page_info = {
            "data": ("📊", "数据分析", "强大的数据处理和可视化工具"),
            "email": ("📧", "邮件助手", "智能邮件撰写和管理系统"),
            "schedule": ("📅", "日程管理", "智能日程规划和提醒服务"),
            "translate": ("🌐", "翻译工具", "多语言实时翻译服务"),
            "summary": ("📋", "内容总结", "文档和网页智能总结工具"),
            "code": ("💻", "代码助手", "编程辅助和代码生成工具")
        }

        icon, title, desc = page_info.get(page_name, ("🔧", "功能开发中", "该功能正在开发中"))

        # 图标
        icon_label = QLabel(icon)
        icon_label.setFont(ModernDesignSystem.get_font('5xl', 'normal'))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('primary.main')};")
        layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(title)
        title_label.setFont(ModernDesignSystem.get_font('2xl', 'bold'))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_800')};")
        layout.addWidget(title_label)

        # 描述
        desc_label = QLabel(desc)
        desc_label.setFont(ModernDesignSystem.get_font('base', 'normal'))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 敬请期待标签
        coming_soon = QLabel("🚧 敬请期待")
        coming_soon.setFont(ModernDesignSystem.get_font('lg', 'medium'))
        coming_soon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        coming_soon.setStyleSheet(f"""
            color: {ModernDesignSystem.get_color('warning.main')};
            background-color: {ModernDesignSystem.get_color('warning.surface')};
            padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('lg')}px;
            border-radius: {ModernDesignSystem.get_border_radius('full')}px;
            margin-top: {ModernDesignSystem.get_spacing('lg')}px;
        """)
        layout.addWidget(coming_soon)

        return page

    def setup_enhanced_status_bar(self):
        """设置增强版状态栏"""
        self.status_bar = EnhancedStatusBar()
        self.setStatusBar(self.status_bar)

        # 设置初始状态
        if self.agent:
            self.status_bar.set_status("AI助手已就绪", "success")
            self.status_bar.set_connection_status(True)
        else:
            self.status_bar.set_status("演示模式运行中", "warning")
            self.status_bar.set_connection_status(False)

    def setup_accessibility(self):
        """设置无障碍功能"""
        self.accessibility_manager = AccessibilityManager(self)

        # 设置Tab键导航顺序
        focusable_widgets = []

        # 收集所有可聚焦的控件
        for button in self.nav_buttons.values():
            focusable_widgets.append(button)

        if hasattr(self, 'chat_input') and self.chat_input:
            focusable_widgets.append(self.chat_input)

        # 设置导航顺序
        KeyboardNavigationHelper.setup_tab_order(focusable_widgets)

    def setup_animations(self):
        """设置动画效果"""
        # 为导航按钮添加动画
        for button in self.nav_buttons.values():
            # 按钮已经在EnhancedNavigationButton中包含动画
            pass

    def show_welcome_animation(self):
        """显示欢迎动画"""
        # 窗口淡入动画
        fade_in = self.animation_manager.create_fade_in_animation(self, 500)
        fade_in.start()

        # 延迟显示欢迎提示
        QTimer.singleShot(1000, self.show_welcome_toast)

    def show_welcome_toast(self):
        """显示欢迎提示"""
        toast = NotificationToast(
            "欢迎使用智能办公助手！开始您的高效办公之旅吧 🚀",
            "info",
            4000,
            self
        )

        # 定位到右上角
        toast.move(self.width() - toast.width() - 20, 20)
        toast.show_toast()

    def switch_page(self, page_id: str):
        """切换页面"""
        page_index = {
            "chat": 0,
            "document": 1,
            "data": 2,
            "email": 3,
            "schedule": 4,
            "translate": 5,
            "summary": 6,
            "code": 7
        }.get(page_id, 0)

        # 更新导航按钮状态
        for btn_id, button in self.nav_buttons.items():
            button.setChecked(btn_id == page_id)

        # 切换页面
        self.content_stack.setCurrentIndex(page_index)

        # 更新状态栏
        page_names = {
            "chat": "智能对话",
            "document": "文档助手",
            "data": "数据分析",
            "email": "邮件助手",
            "schedule": "日程管理",
            "translate": "翻译工具",
            "summary": "内容总结",
            "code": "代码助手"
        }

        page_name = page_names.get(page_id, "未知页面")
        self.status_bar.set_status(f"当前页面: {page_name}")

        # 无障碍公告
        if self.accessibility_manager:
            self.accessibility_manager.announce_to_screen_reader(f"已切换到{page_name}页面")

    def add_welcome_message(self):
        """添加欢迎消息"""
        welcome_msg = """🎉 欢迎使用智能办公助手！

我是您的AI办公伙伴，可以帮助您：

✨ **智能对话** - 回答问题、提供建议、头脑风暴
📄 **文档创建** - 生成各类专业文档
📊 **数据分析** - 处理和分析数据
📧 **邮件助手** - 撰写专业邮件
📅 **日程管理** - 规划和管理时间
🌐 **多语翻译** - 跨语言沟通
📋 **内容总结** - 提取关键信息
💻 **代码助手** - 编程支持

请告诉我您需要什么帮助，我会尽力为您提供最佳的解决方案！✨"""

        message = EnhancedChatMessage(
            welcome_msg,
            is_user=False,
            timestamp=datetime.now().strftime("%H:%M")
        )
        self.chat_layout.addWidget(message)
        self.chat_layout.addStretch()

    def new_chat(self):
        """新建对话"""
        if self.agent:
            self.current_session = self.agent.start_conversation("office_user")

        # 清空聊天记录
        for i in reversed(range(self.chat_layout.count())):
            item = self.chat_layout.itemAt(i)
            if item.widget():
                item.widget().setParent(None)

        # 添加欢迎消息
        self.add_welcome_message()

        # 显示成功提示
        toast = NotificationToast("新对话已创建", "success", 2000, self)
        toast.move(self.width() - toast.width() - 20, 80)
        toast.show_toast()

        # 更新状态栏
        self.status_bar.set_status("新对话已创建", "success")

    def send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.text().strip()
        if not message:
            return

        # 清空输入框
        self.chat_input.clear()

        # 添加用户消息
        user_msg = EnhancedChatMessage(
            message,
            is_user=True,
            timestamp=datetime.now().strftime("%H:%M")
        )
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, user_msg)

        # 滚动到底部
        QTimer.singleShot(50, self.scroll_to_bottom)

        # 显示加载指示器
        loading = LoadingIndicator(24)
        loading_msg = EnhancedChatMessage(
            "AI正在思考中...",
            is_user=False,
            timestamp=datetime.now().strftime("%H:%M")
        )
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, loading_msg)

        # 模拟AI回复
        if self.agent:
            # 这里应该调用实际的AI处理
            QTimer.singleShot(2000, lambda: self.simulate_ai_response(message, loading_msg))
        else:
            QTimer.singleShot(1500, lambda: self.simulate_ai_response(message, loading_msg))

    def simulate_ai_response(self, user_message: str, loading_msg):
        """模拟AI回复"""
        # 移除加载消息
        loading_msg.setParent(None)

        # 生成回复
        if self.agent:
            ai_response = f"您说：{user_message}\n\n我正在为您处理这个请求，请稍等..."
        else:
            ai_response = f"[演示模式] 您的消息：{user_message}\n\n这是一个演示回复。在实际使用中，AI会根据您的问题提供详细的帮助和建议。"

        # 添加AI回复
        ai_msg = EnhancedChatMessage(
            ai_response,
            is_user=False,
            timestamp=datetime.now().strftime("%H:%M")
        )
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, ai_msg)

        # 滚动到底部
        QTimer.singleShot(50, self.scroll_to_bottom)

    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.chat_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def create_document(self, doc_type: str):
        """创建文档"""
        toast = NotificationToast(
            f"正在创建{doc_type}...",
            "info",
            3000,
            self
        )
        toast.move(self.width() - toast.width() - 20, 140)
        toast.show_toast()

        self.status_bar.set_status(f"正在创建{doc_type}...")

    def save_current(self):
        """保存当前内容"""
        toast = NotificationToast("内容已保存", "success", 2000, self)
        toast.move(self.width() - toast.width() - 20, 80)
        toast.show_toast()

        self.status_bar.set_status("内容已保存", "success")

    def show_settings(self):
        """显示设置"""
        # 这里应该打开设置对话框
        toast = NotificationToast("设置功能开发中", "info", 2000, self)
        toast.move(self.width() - toast.width() - 20, 80)
        toast.show_toast()

    def show_help(self):
        """显示帮助"""
        if self.accessibility_manager:
            self.accessibility_manager.show_help()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用属性
    app.setApplicationName("智能办公助手")
    app.setApplicationVersion("2.0 Enhanced")
    app.setOrganizationName("AI Office Assistant")

    # 设置应用样式
    app.setStyle("Fusion")

    # PyQt6默认启用高DPI支持和高质量像素图，无需手动设置

    # 创建并显示主窗口
    window = EnhancedModernOfficeAssistant()
    window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
