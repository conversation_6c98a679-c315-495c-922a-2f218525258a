# -*- coding: utf-8 -*-
"""
智能文档处理Agent - 提示词配置模块

此模块包含所有用于DeepSeek模型的提示词和JSON输出格式定义，
实现提示词与业务逻辑的分离，便于维护和优化。
"""

from typing import Dict, Any, List
import json


class PromptsConfig:
    """提示词配置类"""
    
    # JSON输出格式定义
    DOCUMENT_PLAN_SCHEMA = {
        "type": "object",
        "properties": {
            "document_type": {
                "type": "string",
                "description": "文档类型，如：软件测试文档、技术规范、用户手册等"
            },
            "title": {
                "type": "string", 
                "description": "文档标题"
            },
            "target_words": {
                "type": "integer",
                "description": "目标字数"
            },
            "file_name": {
                "type": "string",
                "description": "保存的文件名"
            },
            "sections": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "章节标题"},
                        "content": {"type": "string", "description": "章节详细内容"},
                        "word_count": {"type": "integer", "description": "预估字数"},
                        "style": {
                            "type": "object",
                            "properties": {
                                "font_size": {"type": "integer"},
                                "bold": {"type": "boolean"},
                                "alignment": {"type": "string"}
                            }
                        }
                    }
                }
            },
            "tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "caption": {"type": "string"},
                        "position": {"type": "string"},
                        "rows": {"type": "integer"},
                        "cols": {"type": "integer"},
                        "data": {
                            "type": "array",
                            "items": {
                                "type": "array",
                                "items": {"type": "string"}
                            }
                        }
                    }
                }
            },
            "metadata": {
                "type": "object",
                "properties": {
                    "author": {"type": "string"},
                    "subject": {"type": "string"},
                    "keywords": {"type": "string"},
                    "description": {"type": "string"}
                }
            },
            "execution_steps": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "integer"},
                        "action": {"type": "string"},
                        "tool": {"type": "string"},
                        "parameters": {"type": "object"}
                    }
                }
            }
        },
        "required": ["document_type", "title", "target_words", "sections", "execution_steps"]
    }
    
    # 系统提示词
    DOCUMENT_ANALYSIS_SYSTEM_PROMPT = """你是一个专业的文档规划专家，能够分析用户需求并生成详细的文档创建计划。

你的任务是：
1. 理解用户的文档需求（类型、内容、字数等）
2. 制定详细的文档结构和内容规划
3. 生成具体的执行步骤
4. 输出标准的JSON格式规划文档

支持的文档类型：
- 软件测试文档：包含测试概述、测试环境、测试策略、测试用例设计、功能测试、性能测试、安全测试、兼容性测试、缺陷管理、测试报告等章节
- 技术规范文档：包含引言、系统概述、功能需求、非功能需求、接口需求、数据需求、安全需求、性能需求、部署需求、维护需求等章节
- 用户手册：包含产品介绍、安装指南、基本操作、高级功能、常见问题、故障排除、技术支持等章节
- 项目计划书：包含项目概述、目标与范围、时间计划、资源配置、风险评估、质量管理、沟通计划、项目监控等章节

请确保：
1. 文档结构合理，章节安排逻辑清晰
2. 内容详实，满足指定的字数要求
3. 执行步骤具体可操作
4. 输出必须是有效的JSON格式

可用的工具函数（必须使用以下确切名称）：
- create_document: 创建新文档
- add_text: 添加文本内容
- add_all_content: 批量添加章节内容
- create_table: 创建表格（不是add_table！）
- set_document_metadata: 设置文档元数据
- save_document: 保存文档
- insert_image: 插入图片
- add_header_footer: 添加页眉页脚

JSON输出示例：
{
    "document_type": "软件测试文档",
    "title": "XXX系统测试文档",
    "target_words": 1000,
    "file_name": "测试文档.docx",
    "sections": [
        {
            "title": "1. 测试概述",
            "content": "详细的测试概述内容...",
            "word_count": 150,
            "style": {"font_size": 14, "bold": true, "alignment": "left"}
        }
    ],
    "tables": [
        {
            "caption": "测试用例表",
            "position": "after_section_4",
            "rows": 5,
            "cols": 4,
            "data": [["用例ID", "测试内容", "预期结果", "实际结果"]]
        }
    ],
    "metadata": {
        "author": "测试团队",
        "subject": "软件测试",
        "keywords": "测试,质量保证,验证",
        "description": "系统功能和性能测试文档"
    },
    "execution_steps": [
        {
            "step": 1,
            "action": "创建文档",
            "tool": "create_document",
            "parameters": {"title": "XXX系统测试文档"}
        },
        {
            "step": 2,
            "action": "添加章节内容",
            "tool": "add_text",
            "parameters": {"text": "章节内容", "style": {...}}
        }
    ]
}"""

    # 内容生成提示词模板
    CONTENT_GENERATION_TEMPLATES = {
        "software_testing": {
            "sections": [
                {
                    "title": "1. 测试概述",
                    "template": """本文档是针对{system_name}的全面测试文档，旨在确保软件产品的质量和可靠性。测试工作将严格按照IEEE 829标准执行，涵盖功能测试、性能测试、安全测试等多个维度。

测试目标：
• 验证软件功能的正确性和完整性
• 评估系统性能是否满足业务需求  
• 确保软件安全性符合行业标准
• 验证用户界面的易用性和一致性
• 评估系统在各种环境下的兼容性

测试范围包括但不限于：核心业务功能、用户界面交互、数据处理逻辑、系统集成接口、异常处理机制等。测试将采用黑盒测试、白盒测试和灰盒测试相结合的方法，确保测试覆盖率达到预期标准。"""
                },
                {
                    "title": "2. 测试环境",
                    "template": """测试环境配置对于确保测试结果的准确性和可重复性至关重要。本项目的测试环境严格按照生产环境规格搭建，以最大程度地模拟实际使用场景。

硬件环境：
• 服务器：Dell PowerEdge R750，配置Intel Xeon处理器、64GB内存
• 网络：千兆以太网，确保网络延迟最小化
• 存储：SSD固态硬盘，保证I/O性能
• 客户端：包含Windows、macOS、Linux多种操作系统

软件环境：
• 操作系统：Windows Server 2022、Ubuntu 20.04 LTS
• 数据库：MySQL 8.0、Redis 6.2
• Web服务器：Nginx 1.20、Apache 2.4
• 测试工具：Selenium WebDriver、JMeter、Postman
• 版本控制：Git 2.35

环境管理策略包括定期备份、版本同步、配置标准化等措施，确保测试环境的稳定性和一致性。"""
                }
            ]
        },
        
        "technical_specification": {
            "sections": [
                {
                    "title": "1. 引言",
                    "template": """本文档是{system_name}的技术规范文档，定义了系统的功能、性能、安全、部署和维护等方面的要求。

文档目的：
• 确保系统设计符合业务需求
• 提供清晰的开发和测试标准
• 指导系统部署和运维
• 便于团队协作和问题追踪

文档范围：
• 系统整体架构
• 核心功能模块
• 接口定义
• 数据结构
• 安全机制
• 性能指标
• 部署方案
• 维护指南"""
                }
            ]
        },
        
        "user_manual": {
            "sections": [
                {
                    "title": "1. 产品介绍",
                    "template": """欢迎使用{system_name}！本用户手册将帮助您快速了解和使用本产品的各项功能。

产品概述：
{system_name}是一款功能强大的{product_type}，旨在为用户提供{main_features}。产品采用先进的技术架构，具有操作简便、功能丰富、性能稳定等特点。

主要特性：
• {feature_1}
• {feature_2}  
• {feature_3}
• {feature_4}

适用场景：
本产品适用于{target_users}，可以有效解决{problems_solved}等问题，提升工作效率和用户体验。"""
                }
            ]
        }
    }

    # 执行步骤模板
    EXECUTION_STEP_TEMPLATES = {
        "create_document_with_content": [
            {
                "step": 1,
                "action": "创建空白文档",
                "tool": "create_document",
                "parameters": {"title": "{title}"}
            },
            {
                "step": 2,
                "action": "设置文档元数据",
                "tool": "set_document_metadata", 
                "parameters": {
                    "title": "{title}",
                    "author": "{author}",
                    "subject": "{subject}",
                    "keywords": "{keywords}",
                    "description": "{description}"
                }
            },
            {
                "step": 3,
                "action": "添加文档内容",
                "tool": "add_sections_content",
                "parameters": {"sections": "{sections}"}
            },
            {
                "step": 4,
                "action": "插入表格",
                "tool": "create_tables",
                "parameters": {"tables": "{tables}"}
            },
            {
                "step": 5,
                "action": "保存文档",
                "tool": "save_document",
                "parameters": {"file_path": "{file_name}", "format_type": "docx"}
            }
        ]
    }

    @classmethod
    def get_document_analysis_prompt(cls) -> str:
        """获取文档分析系统提示词"""
        return cls.DOCUMENT_ANALYSIS_SYSTEM_PROMPT
    
    @classmethod
    def get_content_template(cls, document_type: str, section_title: str) -> str:
        """获取内容生成模板"""
        doc_type_key = document_type.lower().replace(" ", "_").replace("文档", "")
        
        templates = cls.CONTENT_GENERATION_TEMPLATES.get(doc_type_key, {})
        sections = templates.get("sections", [])
        
        for section in sections:
            if section_title.lower() in section["title"].lower():
                return section["template"]
        
        # 默认模板
        return """本章节详细描述了{section_topic}的相关内容。

主要内容包括：
• {content_point_1}
• {content_point_2}
• {content_point_3}
• {content_point_4}

详细说明：
{detailed_description}

实施要点：
1. {implementation_point_1}
2. {implementation_point_2}
3. {implementation_point_3}

注意事项：
{notes_and_considerations}"""

    @classmethod
    def get_execution_steps_template(cls, template_type: str = "create_document_with_content") -> List[Dict[str, Any]]:
        """获取执行步骤模板"""
        return cls.EXECUTION_STEP_TEMPLATES.get(template_type, [])

    @classmethod
    def format_user_prompt(cls, user_input: str) -> str:
        """格式化用户输入为包含JSON要求的提示词"""
        return f"""用户需求：{user_input}

请分析用户需求并生成详细的文档创建计划。输出必须是有效的JSON格式，包含document_type、title、target_words、sections、execution_steps等字段。

请确保：
1. 准确理解用户的文档类型和字数要求
2. 生成合理的章节结构和详细内容
3. 制定具体的执行步骤
4. 输出格式严格遵循JSON标准"""

    @classmethod
    def get_response_format(cls) -> Dict[str, str]:
        """获取JSON响应格式配置"""
        return {"type": "json_object"}


# 文档类型配置
DOCUMENT_TYPE_CONFIG = {
    "软件测试文档": {
        "default_sections": ["测试概述", "测试环境", "测试策略", "测试用例设计", "功能测试", "性能测试", "安全测试", "兼容性测试", "缺陷管理", "测试报告"],
        "typical_word_count": 1000,
        "author": "测试团队",
        "keywords": "测试,质量保证,验证,软件质量"
    },
    "技术规范文档": {
        "default_sections": ["引言", "系统概述", "功能需求", "非功能需求", "接口需求", "数据需求", "安全需求", "性能需求", "部署需求", "维护需求"],
        "typical_word_count": 1500,
        "author": "技术团队",
        "keywords": "技术规范,系统设计,架构,需求"
    },
    "用户手册": {
        "default_sections": ["产品介绍", "安装指南", "快速入门", "基本操作", "高级功能", "设置配置", "常见问题", "故障排除", "技术支持"],
        "typical_word_count": 800,
        "author": "产品团队",
        "keywords": "用户手册,操作指南,帮助文档"
    },
    "项目计划书": {
        "default_sections": ["项目概述", "目标与范围", "时间计划", "资源配置", "团队组织", "风险评估", "质量管理", "沟通计划", "项目监控", "交付成果"],
        "typical_word_count": 1200,
        "author": "项目团队",
        "keywords": "项目管理,计划,执行,监控"
    }
} 