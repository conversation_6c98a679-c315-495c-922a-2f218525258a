# -*- coding: utf-8 -*-
"""
增强版文档规划器模块

集成了丰富的样式功能和主题选择，能够根据用户需求
智能选择合适的文档主题、样式配置和格式方案。
"""

import json
import logging
from typing import Dict, Any, Optional, Callable, List
from openai import OpenAI

from enhanced_prompts_config import EnhancedPromptsConfig, ENHANCED_DOCUMENT_TEMPLATES


class EnhancedDocumentPlanner:
    """增强版文档规划器
    
    分析用户需求并生成包含样式配置的详细文档创建计划，
    智能选择最合适的主题和格式方案。
    """
    
    def __init__(self, deepseek_client: OpenAI, auto_save: bool = False, wps_visible: bool = True):
        """初始化增强版文档规划器
        
        参数:
            deepseek_client: DeepSeek API客户端
            auto_save: 是否自动保存文档，默认False
            wps_visible: WPS是否可见，默认True让用户看到创建过程
        """
        self.client = deepseek_client
        self.logger = logging.getLogger(__name__)
        self.prompts = EnhancedPromptsConfig()
        self.auto_save = auto_save
        self.wps_visible = wps_visible
    
    def analyze_user_request_stream(self, user_input: str, 
                                  callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """分析用户请求并生成增强的文档计划
        
        参数:
            user_input: 用户输入的需求描述
            callback: 回调函数，用于流式输出
            
        返回:
            Dict: 包含增强文档计划的字典
        """
        try:
            if callback:
                callback('analysis', f"📋 开始分析用户需求: {user_input}")
            
            # 智能文档类型检测
            suggestions = self.prompts.get_document_type_suggestions(user_input)
            detected_type = suggestions.get('detected_type', 'general')
            recommended_theme = suggestions.get('recommended_theme', 'professional')
            style_features = suggestions.get('style_features', {})
            
            if callback:
                callback('analysis', f"📋 正在调用DeepSeek模型进行需求分析...")
            
            # 构建增强的系统提示词
            system_prompt = self._build_enhanced_prompt(
                detected_type, recommended_theme, style_features
            )
            
            user_prompt = f"""用户需求：{user_input}

请分析这个需求并生成详细的文档创建计划。

需要考虑的因素：
1. 文档类型：{detected_type}
2. 推荐主题：{recommended_theme}
3. 样式特征：{json.dumps(style_features, ensure_ascii=False, indent=2)}

**重要：必须以有效的JSON格式输出完整的执行计划，不要添加任何其他说明文字！**

**JSON输出格式要求：**

## 📋 必需字段：
- document_type: 文档类型
- title: 文档标题  
- target_words: 目标字数
- file_name: 文件名（.docx格式）
- theme_config: 主题配置
- style_config: 样式配置
- page_setup: 页面设置
- header_footer: 页眉页脚配置
- sections: 章节结构（**重要：内容必须是完整的专业文档**）
- execution_steps: 详细执行步骤

## 🔥 内容质量要求（关键！）：
对于软件测试报告，sections中每个章节的content字段必须包含：

### 引言章节（150-200字）：
- 具体的项目背景描述
- 详细的测试目标和范围
- 测试团队组成和时间安排
- 测试依据和标准

### 测试环境章节（200-250字）：
- 具体硬件配置（CPU型号、内存大小、存储容量）
- 详细软件环境（操作系统版本、数据库版本、中间件版本）
- 网络环境配置
- 测试工具清单和版本

### 测试方法章节（250-300字）：
- 功能测试的具体流程和覆盖范围
- 性能测试的负载模型和指标
- 安全测试的攻击场景和防护验证
- 兼容性测试的环境矩阵
- 测试用例设计方法

### 测试用例章节（300-400字）：
- 具体测试用例编号和名称
- 详细的测试步骤描述
- 测试数据和预期结果
- 用例执行统计和通过率

### 测试结果章节（200-300字）：
- 发现缺陷的详细描述和分类
- 缺陷严重程度统计
- 测试覆盖率数据
- 性能测试具体数值结果

### 结论章节（150-200字）：
- 软件质量的具体评估结论
- 风险点识别和评估
- 具体的改进建议
- 后续测试计划

**禁止使用："描述xxx"、"列出xxx"、"包括xxx"等提示性语言！**
**必须写出具体的、详细的、专业的文档内容！**

确保execution_steps中包含完整的8个步骤（移除save_document）：create_document, apply_document_theme, set_page_style, add_text_with_advanced_style, add_all_content, create_table (可选), set_header_footer_enhanced, set_document_metadata

## ⚠️ 重要提醒：
**输出必须是纯JSON格式，以 {{ 开始，以 }} 结束，不要添加任何解释文字！**
**确保JSON语法正确，所有字符串使用双引号，字段名也用双引号！**"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用DeepSeek API - 流式传输
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                max_tokens=8192,
                temperature=0.1,
                stream=True,      # 启用流式传输
                timeout=30
            )
            
            if callback:
                callback('analysis', f"📋 开始接收模型响应...")
            
            # 处理流式响应
            content = ""
            model_name = "deepseek-chat"
            
            for chunk in response:
                if chunk.choices[0].delta.content:
                    chunk_content = chunk.choices[0].delta.content
                    content += chunk_content
                    
                    # 实时显示接收进度
                    if callback and len(content) % 100 == 0:  # 每100个字符显示一次进度
                        callback('analysis', f"📋 已接收 {len(content)} 字符...")
                
                # 获取模型信息
                if hasattr(chunk, 'model'):
                    model_name = chunk.model
            
            if callback:
                callback('analysis', f"📋 模型响应完成，正在解析结果...")
                callback('analysis', f"📋 模型响应详情：")
                callback('analysis', f"📋 - 模型: {model_name}")
                callback('analysis', f"📋 - 接收字符数: {len(content)}")
                callback('analysis', f"📋 🔍 DeepSeek原始响应内容:")
                callback('analysis', f"📋 ============================================================")
                callback('analysis', f"📋 {content}")
                callback('analysis', f"📋 ============================================================")
            
            try:
                # 清理和解析JSON响应
                cleaned_content = self._clean_json_content(content)
                base_plan = json.loads(cleaned_content)
                
                # 增强文档计划
                enhanced_plan = self._enhance_document_plan(
                    base_plan, detected_type, recommended_theme, style_features
                )
                
                if callback:
                    callback('analysis', f"📋 成功解析文档计划，类型: {enhanced_plan.get('document_type')}")
                
                return {
                    "success": True,
                    "parsed_plan": enhanced_plan,
                    "raw_response": content,
                    "detected_type": detected_type,
                    "recommended_theme": recommended_theme,
                    "style_features": style_features
                }
                
            except json.JSONDecodeError as e:
                error_msg = f"JSON解析失败: {e}"
                self.logger.error(error_msg)
                if callback:
                    callback('analysis', f"❌ {error_msg}")
                
                return {
                    "success": False,
                    "error": error_msg,
                    "raw_response": content
                }
                
        except Exception as e:
            error_msg = f"需求分析过程出错: {e}"
            self.logger.error(error_msg)
            if callback:
                callback('analysis', f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg
            }
    
    def analyze_user_request_pure_json(self, user_input: str) -> Dict[str, Any]:
        """分析用户请求并生成纯净的JSON格式文档计划
        
        专门用于需要纯净JSON输出的场景，不使用流式处理和调试输出
        
        参数:
            user_input: 用户输入的需求描述
            
        返回:
            Dict: 包含增强文档计划的字典
        """
        try:
            # 智能文档类型检测
            suggestions = self.prompts.get_document_type_suggestions(user_input)
            detected_type = suggestions.get('detected_type', 'general')
            recommended_theme = suggestions.get('recommended_theme', 'professional')
            style_features = suggestions.get('style_features', {})
            
            # 构建增强的系统提示词
            system_prompt = self._build_enhanced_prompt(
                detected_type, recommended_theme, style_features
            )
            
            # 严格的用户提示词，强调纯JSON输出
            user_prompt = f"""用户需求：{user_input}

请分析这个需求并生成详细的文档创建计划。

需要考虑的因素：
1. 文档类型：{detected_type}
2. 推荐主题：{recommended_theme}
3. 样式特征：{json.dumps(style_features, ensure_ascii=False, indent=2)}

⚠️ **严格要求：**
1. **必须输出纯净的JSON格式，不能包含任何解释文字、注释或markdown标记**
2. **JSON必须以 {{ 开始，以 }} 结束**
3. **不要添加 ```json 或 ``` 标记**
4. **不要添加任何前后说明文字**

必需的JSON字段：
- document_type: 文档类型
- title: 文档标题  
- target_words: 目标字数
- file_name: 文件名（.docx格式）
- theme_config: 主题配置
- style_config: 样式配置
- page_setup: 页面设置
- header_footer: 页眉页脚配置
- sections: 章节结构（内容必须是完整的专业文档）
- execution_steps: 详细执行步骤（8个步骤：create_document, apply_document_theme, set_page_style, add_text_with_advanced_style, add_all_content, create_table, set_header_footer_enhanced, set_document_metadata）
- wps_config: WPS配置信息

每个execution_step必须包含：step(整数), action(字符串), tool(字符串), parameters(对象)

对于create_document步骤，parameters必须包含：
- title: 文档标题
- visible: true（让用户看到WPS创建过程）

对于create_table步骤，确保parameters包含：
- rows: 行数(整数)
- cols: 列数(整数) 
- data: 二维数组数据
- caption: 表格标题(可选)

输出示例格式：
{{
  "document_type": "软件测试报告",
  "title": "XXX系统测试报告",
  "target_words": 1000,
  "wps_config": {{
    "visible": true,
    "auto_save": false
  }},
  "execution_steps": [
    {{
      "step": 1,
      "action": "创建文档",
      "tool": "create_document",
      "parameters": {{
        "title": "XXX系统测试报告",
        "visible": true
      }}
    }},
    {{
      "step": 6,
      "action": "创建表格",
      "tool": "create_table", 
      "parameters": {{
        "rows": 4,
        "cols": 4,
        "data": [["测试项", "方法", "预期", "结果"], ["功能测试", "黑盒", "通过", "待测"]],
        "caption": "测试用例表"
      }}
    }}
  ]
}}

**注意：不包含save_document步骤，因为保存操作由用户手动完成！**
**再次强调：只输出JSON内容，不要任何其他文字！**"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用DeepSeek API - 非流式传输，确保响应完整性
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                max_tokens=8192,
                temperature=0.1,
                stream=False,  # 关闭流式传输
                timeout=30
            )
            
            # 获取完整响应内容
            content = response.choices[0].message.content.strip()
            
            try:
                # 强化的JSON清理
                cleaned_content = self._clean_json_content(content)
                base_plan = json.loads(cleaned_content)
                
                # 增强文档计划
                enhanced_plan = self._enhance_document_plan(
                    base_plan, detected_type, recommended_theme, style_features
                )
                
                return {
                    "success": True,
                    "parsed_plan": enhanced_plan,
                    "raw_response": content,
                    "cleaned_json": cleaned_content,
                    "detected_type": detected_type,
                    "recommended_theme": recommended_theme,
                    "style_features": style_features
                }
                
            except json.JSONDecodeError as e:
                error_msg = f"JSON解析失败: {e}"
                self.logger.error(error_msg)
                
                return {
                    "success": False,
                    "error": error_msg,
                    "raw_response": content,
                    "cleaned_content": self._clean_json_content(content)
                }
                
        except Exception as e:
            error_msg = f"需求分析过程出错: {e}"
            self.logger.error(error_msg)
            
            return {
                "success": False,
                "error": error_msg
            }
    
    def _build_enhanced_prompt(self, detected_type: str, 
                              recommended_theme: str, 
                              style_features: Dict[str, Any]) -> str:
        """构建增强的分析提示词"""
        base_prompt = self.prompts.get_system_prompt()
        
        enhanced_prompt = f"""{base_prompt}

## 🎯 当前分析上下文

**检测到的文档类型**: {detected_type}
**推荐主题**: {recommended_theme}
**样式特征**: {json.dumps(style_features, ensure_ascii=False, indent=2)}

## 📋 分析和规划要求

基于以上信息，请生成一个包含以下元素的完整文档计划：

### 1. 基础信息
- document_type: 文档类型
- title: 文档标题
- target_words: 目标字数
- file_name: 建议的文件名

### 2. 主题和样式配置
- theme_config: 主题配置
  - theme_name: 主题名称
  - primary_color: 主色调
  - heading_font: 标题字体
  - body_font: 正文字体

### 3. 页面设置
- page_setup: 页面配置
  - page_size: 页面大小
  - orientation: 页面方向
  - margins: 页边距设置

### 4. 页眉页脚
- header_footer: 页眉页脚配置
  - include_page_number: 是否包含页码
  - header_text: 页眉文本
  - footer_text: 页脚文本

### 5. 内容结构
- sections: 章节列表，每个章节包含
  - title: 章节标题  
  - content: 章节详细内容（必须是完整的专业文档内容，不是简短描述！）
  - word_count: 字数
  - style: 样式配置

**重要：sections中的content必须是完整的、专业的、具体的文档内容，达到指定字数要求！
不要只写"描述xxx"、"列出xxx"这样的提示性文字，而要写出真正的文档内容！**

### 6. 执行步骤
- execution_steps: 详细执行步骤数组，每个步骤必须包含以下字段：
  - step: 步骤编号（整数）
  - action: 操作描述（字符串）
  - tool: 工具名称（字符串）
  - parameters: 参数对象

执行步骤必须按以下顺序：
  1. create_document: 创建文档（设置visible=True让用户看到创建过程）
  2. apply_document_theme: 应用文档主题
  3. set_page_style: 设置页面样式
  4. add_text_with_advanced_style: 添加标题（带样式）
  5. add_all_content: 添加主要内容
  6. create_table: 创建表格（如需要）
  7. set_header_footer_enhanced: 设置页眉页脚
  8. set_document_metadata: 设置元数据
**注意：不包含save_document步骤，保存操作由用户手动完成！**

示例格式：
[
  {{
    "step": 1,
    "action": "创建文档",
    "tool": "create_document",
    "parameters": {{"title": "文档标题"}}
  }},
  {{
    "step": 2,
    "action": "应用文档主题",
    "tool": "apply_document_theme", 
    "parameters": {{"theme_name": "professional"}}
  }}
]

## 🔥 内容生成要求（重要！）

对于软件测试报告类文档，请生成真正的专业内容：

### 引言部分应包含：
- 测试背景和目的的详细说明
- 测试范围和限制条件
- 参与测试的人员和时间安排

### 测试环境部分应包含：
- 具体的硬件配置信息（CPU、内存、存储等）
- 详细的软件环境（操作系统版本、中间件版本、数据库版本）
- 网络环境配置
- 测试工具和版本信息

### 测试方法部分应包含：
- 各种测试方法的详细说明（功能测试、性能测试、安全测试等）
- 测试策略和测试流程
- 测试标准和验收准则

### 测试用例部分应包含：
- 具体的测试用例编号、名称、步骤
- 实际的测试数据和预期结果
- 测试执行情况统计

### 测试结果部分应包含：
- 发现的具体缺陷和bug描述
- 缺陷等级分类和统计
- 测试覆盖率数据
- 性能测试结果数据

### 结论部分应包含：
- 软件质量评估的具体结论
- 风险评估
- 改进建议和后续计划

**每个章节的content字段必须包含上述具体内容，不能只是简短的一句话描述！**

## 技术要求：
- 颜色格式使用RGB数字格式，如 "79,129,189"（不要使用#十六进制）
- 边距使用数字（厘米），如 2.54（不要使用单位字符串）
- 必须先创建文档，再应用样式
- 确保总字数达到用户要求的目标字数
"""
        
        return enhanced_prompt
    
    def _clean_json_content(self, content: str) -> str:
        """清理JSON内容，移除可能的非JSON字符"""
        # 去除首尾空白
        content = content.strip()
        
        # 找到第一个 { 和最后一个 }
        start_idx = content.find('{')
        end_idx = content.rfind('}')
        
        if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
            # 如果找不到有效的JSON边界，返回原内容
            return content
        
        # 提取JSON部分
        json_content = content[start_idx:end_idx + 1]
        
        # 更强大的JSON清理：移除所有非JSON内容
        import re
        
        # 1. 移除可能的markdown代码块标记
        json_content = re.sub(r'```json\s*', '', json_content)
        json_content = re.sub(r'```\s*$', '', json_content)
        
        # 2. 移除前后的解释文字（以换行符分隔的非JSON内容）
        lines = json_content.split('\n')
        json_lines = []
        in_json = False
        brace_count = 0
        
        for line in lines:
            stripped_line = line.strip()
            if not in_json and stripped_line.startswith('{'):
                in_json = True
                brace_count += stripped_line.count('{') - stripped_line.count('}')
                json_lines.append(line)
            elif in_json:
                brace_count += stripped_line.count('{') - stripped_line.count('}')
                json_lines.append(line)
                if brace_count <= 0:
                    break
        
        json_content = '\n'.join(json_lines)
        
        # 3. 修复可能的换行符问题
        json_content = re.sub(r'(?<!\\)\n(?=[^"]*"[^"]*$)', '\\n', json_content)
        
        # 4. 移除可能的注释行
        json_content = re.sub(r'^\s*//.*$', '', json_content, flags=re.MULTILINE)
        
        # 5. 确保JSON的开始和结束
        json_content = json_content.strip()
        if not json_content.startswith('{'):
            json_content = '{' + json_content
        if not json_content.endswith('}'):
            json_content = json_content + '}'
        
        return json_content
    
    def _enhance_document_plan(self, base_plan: Dict[str, Any], 
                             detected_type: str, 
                             recommended_theme: str,
                             style_features: Dict[str, Any]) -> Dict[str, Any]:
        """增强文档计划，添加样式和主题配置"""
        
        # 如果计划中没有主题配置，添加默认配置
        if "theme_config" not in base_plan:
            base_plan["theme_config"] = {
                "theme_name": recommended_theme,
                "primary_color": "79,129,189",
                "heading_font": "微软雅黑",
                "body_font": "宋体"
            }
        
        # 如果计划中没有页面设置，添加默认配置
        if "page_setup" not in base_plan:
            base_plan["page_setup"] = {
                "page_size": "A4",
                "orientation": "portrait",
                "margins": {
                    "top": 2.54,
                    "bottom": 2.54,
                    "left": 3.18,
                    "right": 3.18
                }
            }
        
        # 如果计划中没有页眉页脚设置，添加默认配置
        if "header_footer" not in base_plan:
            base_plan["header_footer"] = {
                "include_page_number": True,
                "header_text": "",
                "footer_text": ""
            }
        
        # 添加WPS配置信息
        if "wps_config" not in base_plan:
            base_plan["wps_config"] = {
                "visible": self.wps_visible,
                "auto_save": self.auto_save
            }
        
        # 增强执行步骤
        if "execution_steps" in base_plan:
            execution_steps = base_plan["execution_steps"]
            
            # 修复步骤格式，确保每个步骤都有正确的字段
            fixed_steps = []
            for i, step in enumerate(execution_steps):
                if isinstance(step, dict):
                    # 获取工具名称，处理不同的格式
                    tool_name = step.get("tool", "")
                    if not tool_name and "step" in step:
                        # 如果tool字段为空，但step字段包含工具名称（DeepSeek有时会这样返回）
                        step_value = step.get("step")
                        if isinstance(step_value, str) and step_value in ["create_document", "apply_document_theme", "set_page_style", "add_text_with_advanced_style", "add_all_content", "create_table", "set_header_footer_enhanced", "set_document_metadata"]:
                            tool_name = step_value
                    
                    # 确保有有效的工具名称
                    if not tool_name:
                        # 根据步骤序号推断工具名称
                        step_number = step.get("step", i + 1)
                        default_tools = [
                            "create_document", "apply_document_theme", "set_page_style", 
                            "add_text_with_advanced_style", "add_all_content", "create_table",
                            "set_header_footer_enhanced", "set_document_metadata"
                        ]
                        if isinstance(step_number, int) and 1 <= step_number <= len(default_tools):
                            tool_name = default_tools[step_number - 1]
                    
                    # 特殊处理：修复 add_all_content 的 sections 参数
                    if tool_name == "add_all_content":
                        parameters = step.get("parameters", {})
                        sections_in_params = parameters.get("sections", [])
                        sections_in_plan = base_plan.get("sections", [])
                        
                        # 如果执行步骤中的sections不完整，使用计划中的完整sections
                        if len(sections_in_params) < len(sections_in_plan):
                            parameters["sections"] = sections_in_plan
                            step["parameters"] = parameters
                    
                    # 特殊处理：确保 create_document 步骤包含 visible 参数
                    if tool_name == "create_document":
                        parameters = step.get("parameters", {})
                        if "visible" not in parameters:
                            parameters["visible"] = self.wps_visible
                            step["parameters"] = parameters
                    
                    # 确保步骤有所有必需的字段
                    fixed_step = {
                        "step": step.get("step", i + 1),
                        "action": step.get("action", self._get_action_name(tool_name)),
                        "tool": tool_name,
                        "parameters": step.get("parameters", {})
                    }
                    fixed_steps.append(fixed_step)
                else:
                    # 已经是正确格式，直接使用
                    fixed_steps.append(step)
            
            # 修复参数格式问题
            for step in fixed_steps:
                self._fix_step_parameters(step)
            
            # 确保步骤顺序正确（创建文档必须在第一步）
            fixed_steps = self._ensure_correct_order(fixed_steps, base_plan)
            
            base_plan["execution_steps"] = fixed_steps
        
        # 应用样式特征
        if style_features:
            # 更新主题配置
            theme_config = base_plan.get("theme_config", {})
            if "title_style" in style_features:
                title_style = style_features["title_style"]
                if "color" in title_style:
                    theme_config["primary_color"] = title_style["color"]
            
            # 更新页面设置
            if "margin_adjustments" in style_features:
                margins = style_features["margin_adjustments"]
                page_setup = base_plan.get("page_setup", {})
                page_setup.update(margins)
        
        return base_plan
    
    def _get_action_name(self, tool_name: str) -> str:
        """根据工具名获取操作描述"""
        action_mapping = {
            "create_document": "创建文档",
            "apply_document_theme": "应用文档主题",
            "set_page_style": "设置页面样式",
            "add_text_with_advanced_style": "添加标题文本",
            "add_all_content": "添加文档内容",
            "create_table": "创建表格",
            "set_header_footer_enhanced": "设置页眉页脚",
            "set_document_metadata": "设置文档元数据",
            "save_document": "保存文档",
            "add_text": "添加文本",
            "add_header_footer": "添加页眉页脚"
        }
        return action_mapping.get(tool_name, f"执行{tool_name}")
    
    def _fix_step_parameters(self, step: Dict[str, Any]):
        """修复步骤参数格式问题"""
        parameters = step.get("parameters", {})
        tool_name = step.get("tool", "")
        
        # 修复颜色格式（从#十六进制转换为RGB）
        if "primary_color" in parameters:
            color = parameters["primary_color"]
            if color.startswith("#"):
                parameters["primary_color"] = self._hex_to_rgb(color)
        
        # 修复边距格式
        if "margins" in parameters:
            margins = parameters["margins"]
            if isinstance(margins, dict):
                # 处理字典格式的margins
                for key, value in margins.items():
                    if isinstance(value, str) and value.endswith("cm"):
                        margins[key] = float(value.replace("cm", ""))
            elif isinstance(margins, (int, float)):
                # 处理单一数值的margins，转换为标准字典格式
                margin_value = float(margins)
                parameters["margins"] = {
                    "top": margin_value,
                    "bottom": margin_value,
                    "left": margin_value,
                    "right": margin_value
                }
        
        # 修复表格创建参数
        if tool_name == "create_table":
            # 确保必需的参数存在
            if "rows" not in parameters:
                parameters["rows"] = 3
            if "cols" not in parameters:
                # 检查是否有columns参数（DeepSeek有时会用这个）
                if "columns" in parameters:
                    parameters["cols"] = parameters.pop("columns")
                else:
                    parameters["cols"] = 3
            
            # 确保data参数格式正确
            rows = parameters.get("rows", 3)
            cols = parameters.get("cols", 3)
            
            if "data" not in parameters or not parameters["data"]:
                # 生成默认的表格数据
                if "caption" in parameters and "测试" in str(parameters["caption"]):
                    # 测试相关表格的默认数据
                    default_data = [
                        ["测试项", "测试方法", "预期结果", "实际结果"],
                        ["功能测试", "黑盒测试", "功能正常", "待测试"],
                        ["性能测试", "负载测试", "响应时间<2s", "待测试"]
                    ]
                else:
                    # 通用表格的默认数据
                    default_data = [
                        [f"标题{j+1}" for j in range(cols)]
                    ]
                    for i in range(1, rows):
                        default_data.append([f"内容{i}-{j+1}" for j in range(cols)])
                
                # 调整数据到指定的行列数
                if len(default_data) > rows:
                    default_data = default_data[:rows]
                elif len(default_data) < rows:
                    for i in range(len(default_data), rows):
                        default_data.append([f"内容{i+1}-{j+1}" for j in range(cols)])
                
                for row in default_data:
                    if len(row) > cols:
                        row[:] = row[:cols]
                    elif len(row) < cols:
                        row.extend([f"内容-{j+1}" for j in range(len(row), cols)])
                
                parameters["data"] = default_data
            else:
                # 验证现有数据格式
                data = parameters["data"]
                if isinstance(data, list):
                    # 确保数据是二维数组
                    for i, row in enumerate(data):
                        if not isinstance(row, list):
                            data[i] = [str(row)]
                    
                    # 调整行数
                    if len(data) > rows:
                        data[:] = data[:rows]
                    elif len(data) < rows:
                        for i in range(len(data), rows):
                            data.append([f"内容{i+1}-{j+1}" for j in range(cols)])
                    
                    # 调整每行的列数
                    for row in data:
                        if len(row) > cols:
                            row[:] = row[:cols]
                        elif len(row) < cols:
                            row.extend([f"内容-{j+1}" for j in range(len(row), cols)])
                    
                    parameters["data"] = data
            
            # 修复style参数 - TableData期望的是字符串，不是字典
            if "style" in parameters and isinstance(parameters["style"], dict):
                style_dict = parameters["style"]
                # 根据样式属性推断表格样式名称
                if style_dict.get("header_row"):
                    parameters["style"] = "Table Grid"  # WPS表格样式名称
                elif style_dict.get("banded_rows"):
                    parameters["style"] = "Table List 1"
                else:
                    parameters["style"] = "Table Normal"
            elif "style" not in parameters:
                parameters["style"] = None  # 使用默认样式
            
            # 确保行列数与数据一致
            if parameters["data"]:
                parameters["rows"] = len(parameters["data"])
                if parameters["data"][0]:
                    parameters["cols"] = len(parameters["data"][0])
            
            # 移除不需要的参数（如果存在）
            if "columns" in parameters:
                del parameters["columns"]
        
        # 修复元数据设置参数
        if tool_name == "set_document_metadata":
            # 移除可能导致错误的参数
            if "company" in parameters:
                # WPS可能不支持company字段，将其合并到description中
                description = parameters.get("description", "")
                company = parameters.pop("company")
                if company and company not in description:
                    parameters["description"] = f"{description} - {company}".strip(" -")
        
        # 修复其他数值参数
        numeric_params = ["font_size", "line_spacing", "margin_top", "margin_bottom", 
                         "margin_left", "margin_right"]
        for param in numeric_params:
            if param in parameters and isinstance(parameters[param], str):
                try:
                    parameters[param] = float(parameters[param].replace("pt", "").replace("cm", ""))
                except ValueError:
                    pass
    
    def _hex_to_rgb(self, hex_color: str) -> str:
        """将十六进制颜色转换为RGB格式"""
        hex_color = hex_color.lstrip("#")
        if len(hex_color) == 6:
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            return f"{r},{g},{b}"
        return "79,129,189"  # 默认颜色
    
    def _ensure_correct_order(self, steps: List[Dict[str, Any]], 
                            plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """确保执行步骤的正确顺序"""
        # 定义正确的工具执行顺序（移除save_document，保存由用户手动完成）
        correct_order = [
            "create_document",
            "apply_document_theme", 
            "set_page_style",
            "add_text_with_advanced_style",
            "add_all_content",
            "create_table",
            "set_header_footer_enhanced",
            "set_document_metadata"
        ]
        
        # 重新排序步骤
        ordered_steps = []
        step_counter = 1
        
        for tool_name in correct_order:
            for step in steps:
                if step.get("tool") == tool_name:
                    step["step"] = step_counter
                    ordered_steps.append(step)
                    step_counter += 1
                    break
        
        # 添加任何未包含在标准顺序中的其他步骤
        for step in steps:
            if step not in ordered_steps:
                step["step"] = step_counter
                ordered_steps.append(step)
                step_counter += 1
        
        return ordered_steps


class StreamingEnhancedDocumentPlanner(EnhancedDocumentPlanner):
    """流式处理的增强版文档规划器"""
    
    def __init__(self, deepseek_client: OpenAI, auto_save: bool = False, wps_visible: bool = True):
        """初始化流式增强版文档规划器
        
        参数:
            deepseek_client: DeepSeek API客户端
            auto_save: 是否自动保存文档，默认False
            wps_visible: WPS是否可见，默认True让用户看到创建过程
        """
        super().__init__(deepseek_client, auto_save, wps_visible)
    
    def analyze_user_request_stream(self, user_input: str,
                                  callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """流式分析用户需求"""
        result = super().analyze_user_request_stream(user_input, callback)
        
        # 如果分析成功，输出详细的计划信息
        if result["success"] and callback:
            plan = result["parsed_plan"]
            self._output_plan_details(plan, callback)
        
        return result
    
    def analyze_user_request_pure_json(self, user_input: str) -> Dict[str, Any]:
        """生成纯净JSON格式的文档计划
        
        调用父类的纯JSON方法，确保输出格式纯净
        
        参数:
            user_input: 用户输入的需求描述
            
        返回:
            Dict: 包含增强文档计划的字典
        """
        return super().analyze_user_request_pure_json(user_input)
    
    def _output_plan_details(self, plan: Dict[str, Any], 
                           callback: Callable[[str, str], None]):
        """输出计划详情"""
        if callback:
            callback('analysis', f"📋 📋 增强后的文档计划:")
            callback('analysis', f"📋 - 文档类型: {plan.get('document_type')}")
            callback('analysis', f"📋 - 标题: {plan.get('title')}")
            callback('analysis', f"📋 - 目标字数: {plan.get('target_words')}")
            callback('analysis', f"📋 - 章节数: {len(plan.get('sections', []))}")
            callback('analysis', f"📋 - 执行步骤数: {len(plan.get('execution_steps', []))}")
            
            # 输出主题配置
            theme_config = plan.get("theme_config", {})
            if theme_config:
                callback('analysis', f"📋 🎨 主题配置:")
                callback('analysis', f"📋   - 主题: {theme_config.get('theme_name')}")
                callback('analysis', f"📋   - 主色调: {theme_config.get('primary_color')}")
                callback('analysis', f"📋   - 标题字体: {theme_config.get('heading_font')}")
                callback('analysis', f"📋   - 正文字体: {theme_config.get('body_font')}")
            
            # 输出执行步骤概览
            execution_steps = plan.get("execution_steps", [])
            if execution_steps:
                callback('analysis', f"📋 🔧 生成的执行步骤:")
                for step in execution_steps[:5]:  # 只显示前5个步骤
                    action = step.get('action', '未知操作')
                    tool_name = step.get('tool', '未知工具')
                    callback('analysis', f"📋   {step.get('step')}. {action} [{tool_name}]")
                
                if len(execution_steps) > 5:
                    callback('analysis', f"📋   ... 还有 {len(execution_steps) - 5} 个步骤")
                
                # 检查create_table步骤
                table_steps = [s for s in execution_steps if s.get('tool') == 'create_table']
                if table_steps:
                    callback('analysis', f"📋 📊 表格创建步骤:")
                    for step in table_steps:
                        params = step.get('parameters', {})
                        rows = params.get('rows', 'N/A')
                        cols = params.get('cols', 'N/A')
                        caption = params.get('caption', '无标题')
                        has_data = bool(params.get('data'))
                        callback('analysis', f"📋   - {caption}: {rows}行×{cols}列, 数据: {'有' if has_data else '无'}")


# 使用示例
if __name__ == "__main__":
    import os
    from openai import OpenAI
    
    # 初始化DeepSeek客户端
    client = OpenAI(
        api_key="sk-9f4b5af7ea4d467985314a8890339cbf",
        base_url="https://api.deepseek.com"
    )
    
    # 创建增强版规划器
    planner = StreamingEnhancedDocumentPlanner(client)
    
    # 测试用例
    test_input = "帮我创建一个软件测试报告，1000字左右，需要包含测试用例表格"
    
    print("=== 测试1: 流式输出（带调试信息）===")
    def print_callback(msg_type: str, content: str):
        print(f"[{msg_type}] {content}")
    
    # 流式分析
    result = planner.analyze_user_request_stream(test_input, print_callback)
    
    if result["success"]:
        print("✅ 流式分析成功!")
        print(f"文档类型: {result['detected_type']}")
        print(f"推荐主题: {result['recommended_theme']}")
    else:
        print(f"❌ 流式分析失败: {result['error']}")
    
    print("\n" + "="*60 + "\n")
    
    print("=== 测试2: 纯JSON输出（无调试信息）===")
    
    # 纯JSON分析
    json_result = planner.analyze_user_request_pure_json(test_input)
    
    if json_result["success"]:
        print("✅ 纯JSON分析成功!")
        print("原始响应:")
        print(json_result.get("raw_response", "")[:200] + "...")
        print("\n清理后的JSON:")
        print(json_result.get("cleaned_json", "")[:200] + "...")
        
        # 检查表格步骤
        plan = json_result["parsed_plan"]
        table_steps = [s for s in plan.get("execution_steps", []) if s.get("tool") == "create_table"]
        print(f"\n🔍 发现 {len(table_steps)} 个表格创建步骤:")
        for i, step in enumerate(table_steps, 1):
            params = step.get("parameters", {})
            print(f"  {i}. {step.get('action', '创建表格')}")
            print(f"     - 行数: {params.get('rows', 'N/A')}")
            print(f"     - 列数: {params.get('cols', 'N/A')}")
            print(f"     - 标题: {params.get('caption', '无')}")
            print(f"     - 数据: {'有' if params.get('data') else '无'}")
            if params.get('data'):
                print(f"     - 数据示例: {params['data'][0] if params['data'] else '空'}")
    else:
        print(f"❌ 纯JSON分析失败: {json_result['error']}")
        if "raw_response" in json_result:
            print("原始响应:")
            print(json_result["raw_response"][:500])
        if "cleaned_content" in json_result:
            print("清理后内容:")
            print(json_result["cleaned_content"][:500])
    
    print("\n=== 测试完成 ===")
    print("💡 使用提示:")
    print("1. 对于需要纯JSON输出的场景，使用 analyze_user_request_pure_json()")
    print("2. 对于需要实时反馈的场景，使用 analyze_user_request_stream()")
    print("3. 表格创建问题已修复，会自动生成默认数据") 