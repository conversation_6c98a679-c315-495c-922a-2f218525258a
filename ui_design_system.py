# -*- coding: utf-8 -*-
"""
现代化UI设计系统 - PyQt6界面优化
统一的设计语言、颜色方案、字体、间距和组件样式
"""

from typing import Dict, Any, Optional
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette, QLinearGradient, QBrush
from PyQt6.QtWidgets import QWidget, QGraphicsDropShadowEffect


class ModernDesignSystem:
    """现代化设计系统类"""
    
    # 颜色系统 - 基于现代设计趋势
    COLORS = {
        # 主色调 - 深蓝紫渐变
        'primary': {
            'main': '#4F46E5',      # 靛蓝
            'light': '#6366F1',     # 浅靛蓝
            'dark': '#3730A3',      # 深靛蓝
            'surface': '#EEF2FF',   # 主色表面
        },
        
        # 辅助色调
        'secondary': {
            'main': '#06B6D4',      # 青色
            'light': '#22D3EE',     # 浅青色
            'dark': '#0891B2',      # 深青色
            'surface': '#ECFEFF',   # 辅助色表面
        },
        
        # 成功色
        'success': {
            'main': '#10B981',      # 绿色
            'light': '#34D399',     # 浅绿色
            'dark': '#059669',      # 深绿色
            'surface': '#ECFDF5',   # 成功色表面
        },
        
        # 警告色
        'warning': {
            'main': '#F59E0B',      # 橙色
            'light': '#FBBF24',     # 浅橙色
            'dark': '#D97706',      # 深橙色
            'surface': '#FFFBEB',   # 警告色表面
        },
        
        # 错误色
        'error': {
            'main': '#EF4444',      # 红色
            'light': '#F87171',     # 浅红色
            'dark': '#DC2626',      # 深红色
            'surface': '#FEF2F2',   # 错误色表面
        },
        
        # 中性色系
        'neutral': {
            'white': '#FFFFFF',
            'gray_50': '#F9FAFB',
            'gray_100': '#F3F4F6',
            'gray_200': '#E5E7EB',
            'gray_300': '#D1D5DB',
            'gray_400': '#9CA3AF',
            'gray_500': '#6B7280',
            'gray_600': '#4B5563',
            'gray_700': '#374151',
            'gray_800': '#1F2937',
            'gray_900': '#111827',
        },
        
        # 背景色系
        'background': {
            'primary': '#FFFFFF',
            'secondary': '#F8FAFC',
            'tertiary': '#F1F5F9',
            'elevated': '#FFFFFF',
        }
    }
    
    # 字体系统
    TYPOGRAPHY = {
        'font_family': {
            'primary': 'Microsoft YaHei, Segoe UI, system-ui, sans-serif',
            'secondary': 'SF Pro Display, Helvetica Neue, Arial, sans-serif',
            'mono': 'SF Mono, Monaco, Consolas, monospace',
        },
        
        'font_sizes': {
            'xs': 12,
            'sm': 14,
            'base': 16,
            'lg': 18,
            'xl': 20,
            '2xl': 24,
            '3xl': 30,
            '4xl': 36,
            '5xl': 48,
        },
        
        'font_weights': {
            'light': QFont.Weight.Light,
            'normal': QFont.Weight.Normal,
            'medium': QFont.Weight.Medium,
            'semibold': QFont.Weight.DemiBold,
            'bold': QFont.Weight.Bold,
        },
        
        'line_heights': {
            'tight': 1.25,
            'normal': 1.5,
            'relaxed': 1.75,
        }
    }
    
    # 间距系统 (基于8px网格)
    SPACING = {
        'xs': 4,    # 0.25rem
        'sm': 8,    # 0.5rem
        'md': 16,   # 1rem
        'lg': 24,   # 1.5rem
        'xl': 32,   # 2rem
        '2xl': 48,  # 3rem
        '3xl': 64,  # 4rem
        '4xl': 96,  # 6rem
    }
    
    # 圆角系统
    BORDER_RADIUS = {
        'none': 0,
        'sm': 4,
        'md': 8,
        'lg': 12,
        'xl': 16,
        '2xl': 24,
        'full': 9999,
    }
    
    # 阴影系统
    SHADOWS = {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    }
    
    # 动画系统
    ANIMATIONS = {
        'duration': {
            'fast': 150,
            'normal': 300,
            'slow': 500,
        },
        'easing': {
            'ease_in': QEasingCurve.Type.InQuad,
            'ease_out': QEasingCurve.Type.OutQuad,
            'ease_in_out': QEasingCurve.Type.InOutQuad,
            'bounce': QEasingCurve.Type.OutBounce,
        }
    }
    
    @classmethod
    def get_color(cls, color_path: str) -> str:
        """获取颜色值
        
        Args:
            color_path: 颜色路径，如 'primary.main' 或 'neutral.gray_500'
        
        Returns:
            颜色的十六进制值
        """
        keys = color_path.split('.')
        color_dict = cls.COLORS
        
        for key in keys:
            if key in color_dict:
                color_dict = color_dict[key]
            else:
                return '#000000'  # 默认黑色
        
        return color_dict if isinstance(color_dict, str) else '#000000'
    
    @classmethod
    def get_font(cls, size: str = 'base', weight: str = 'normal', family: str = 'primary') -> QFont:
        """获取字体对象
        
        Args:
            size: 字体大小键名
            weight: 字体粗细键名
            family: 字体族键名
        
        Returns:
            QFont对象
        """
        font_size = cls.TYPOGRAPHY['font_sizes'].get(size, 16)
        font_weight = cls.TYPOGRAPHY['font_weights'].get(weight, QFont.Weight.Normal)
        font_family = cls.TYPOGRAPHY['font_family'].get(family, 'Microsoft YaHei')
        
        font = QFont(font_family.split(',')[0].strip())
        font.setPointSize(font_size)
        font.setWeight(font_weight)
        
        return font
    
    @classmethod
    def get_spacing(cls, size: str) -> int:
        """获取间距值
        
        Args:
            size: 间距大小键名
        
        Returns:
            间距像素值
        """
        return cls.SPACING.get(size, 16)
    
    @classmethod
    def get_border_radius(cls, size: str) -> int:
        """获取圆角值
        
        Args:
            size: 圆角大小键名
        
        Returns:
            圆角像素值
        """
        return cls.BORDER_RADIUS.get(size, 8)
    
    @classmethod
    def create_shadow_effect(cls, shadow_type: str = 'md') -> QGraphicsDropShadowEffect:
        """创建阴影效果
        
        Args:
            shadow_type: 阴影类型
        
        Returns:
            QGraphicsDropShadowEffect对象
        """
        shadow = QGraphicsDropShadowEffect()
        
        if shadow_type == 'sm':
            shadow.setBlurRadius(4)
            shadow.setOffset(0, 1)
            shadow.setColor(QColor(0, 0, 0, 13))  # 5% opacity
        elif shadow_type == 'md':
            shadow.setBlurRadius(8)
            shadow.setOffset(0, 4)
            shadow.setColor(QColor(0, 0, 0, 25))  # 10% opacity
        elif shadow_type == 'lg':
            shadow.setBlurRadius(16)
            shadow.setOffset(0, 8)
            shadow.setColor(QColor(0, 0, 0, 25))  # 10% opacity
        elif shadow_type == 'xl':
            shadow.setBlurRadius(32)
            shadow.setOffset(0, 16)
            shadow.setColor(QColor(0, 0, 0, 25))  # 10% opacity
        
        return shadow


class ModernStyleSheets:
    """现代化样式表集合"""
    
    @staticmethod
    def get_main_window_style() -> str:
        """主窗口样式"""
        return f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('background.secondary')}, 
                    stop:1 {ModernDesignSystem.get_color('background.tertiary')});
                font-family: {ModernDesignSystem.TYPOGRAPHY['font_family']['primary']};
            }}
        """
    
    @staticmethod
    def get_button_style() -> str:
        """按钮样式"""
        return f"""
            QPushButton {{
                background-color: {ModernDesignSystem.get_color('primary.main')};
                color: white;
                border: none;
                padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('lg')}px;
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                font-weight: 600;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                font-family: {ModernDesignSystem.TYPOGRAPHY['font_family']['primary']};
            }}
            QPushButton:hover {{
                background-color: {ModernDesignSystem.get_color('primary.light')};
            }}
            QPushButton:pressed {{
                background-color: {ModernDesignSystem.get_color('primary.dark')};
            }}
            QPushButton:disabled {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_300')};
                color: {ModernDesignSystem.get_color('neutral.gray_500')};
            }}
        """
    
    @staticmethod
    def get_input_style() -> str:
        """输入框样式"""
        return f"""
            QLineEdit, QTextEdit {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 2px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('md')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                font-family: {ModernDesignSystem.TYPOGRAPHY['font_family']['primary']};
                color: {ModernDesignSystem.get_color('neutral.gray_800')};
            }}
            QLineEdit:focus, QTextEdit:focus {{
                border-color: {ModernDesignSystem.get_color('primary.main')};
                outline: none;
            }}
            QLineEdit:hover, QTextEdit:hover {{
                border-color: {ModernDesignSystem.get_color('neutral.gray_300')};
            }}
        """
    
    @staticmethod
    def get_scrollbar_style() -> str:
        """滚动条样式"""
        return f"""
            QScrollBar:vertical {{
                border: none;
                background: {ModernDesignSystem.get_color('neutral.gray_100')};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {ModernDesignSystem.get_color('neutral.gray_300')};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {ModernDesignSystem.get_color('neutral.gray_400')};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
        """

    @staticmethod
    def get_card_style() -> str:
        """卡片样式"""
        return f"""
            .ModernCard {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                padding: {ModernDesignSystem.get_spacing('lg')}px;
            }}
            .ModernCard:hover {{
                border-color: {ModernDesignSystem.get_color('primary.main')};
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
            }}
        """

    @staticmethod
    def get_navigation_style() -> str:
        """导航样式"""
        return f"""
            .NavigationPanel {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border-right: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
            }}
            .NavButton {{
                background: transparent;
                border: none;
                text-align: left;
                padding: {ModernDesignSystem.get_spacing('md')}px;
                margin: 2px {ModernDesignSystem.get_spacing('md')}px;
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_700')};
            }}
            .NavButton:hover {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_100')};
            }}
            .NavButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('primary.surface')},
                    stop:1 {ModernDesignSystem.get_color('primary.surface')});
                border-left: 3px solid {ModernDesignSystem.get_color('primary.main')};
                color: {ModernDesignSystem.get_color('primary.main')};
                font-weight: 600;
            }}
        """

    @staticmethod
    def get_chat_bubble_style() -> str:
        """聊天气泡样式"""
        return f"""
            .ChatBubbleUser {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('primary.main')},
                    stop:1 {ModernDesignSystem.get_color('primary.light')});
                color: white;
                border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px {ModernDesignSystem.get_spacing('lg')}px;
                margin: {ModernDesignSystem.get_spacing('sm')}px;
                max-width: 70%;
            }}
            .ChatBubbleAI {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                color: {ModernDesignSystem.get_color('neutral.gray_800')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px {ModernDesignSystem.get_spacing('lg')}px;
                margin: {ModernDesignSystem.get_spacing('sm')}px;
                max-width: 70%;
            }}
            .ChatBubbleSystem {{
                background-color: {ModernDesignSystem.get_color('warning.surface')};
                color: {ModernDesignSystem.get_color('warning.dark')};
                border: 1px solid {ModernDesignSystem.get_color('warning.light')};
                border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px {ModernDesignSystem.get_spacing('lg')}px;
                margin: {ModernDesignSystem.get_spacing('sm')}px;
                max-width: 70%;
            }}
        """

    @staticmethod
    def get_group_box_style() -> str:
        """分组框样式"""
        return f"""
            QGroupBox {{
                font-weight: 600;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_700')};
                border: 2px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                margin: {ModernDesignSystem.get_spacing('md')}px 0;
                padding-top: {ModernDesignSystem.get_spacing('lg')}px;
                background-color: {ModernDesignSystem.get_color('background.primary')};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {ModernDesignSystem.get_spacing('md')}px;
                padding: 0 {ModernDesignSystem.get_spacing('sm')}px;
                background-color: {ModernDesignSystem.get_color('background.primary')};
            }}
        """

    @staticmethod
    def get_combo_box_style() -> str:
        """下拉框样式"""
        return f"""
            QComboBox {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 2px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('md')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_800')};
                min-height: 20px;
            }}
            QComboBox:hover {{
                border-color: {ModernDesignSystem.get_color('neutral.gray_300')};
            }}
            QComboBox:focus {{
                border-color: {ModernDesignSystem.get_color('primary.main')};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {ModernDesignSystem.get_color('neutral.gray_500')};
                margin-right: 5px;
            }}
        """


class AnimationHelper:
    """动画辅助类"""

    @staticmethod
    def create_fade_animation(widget: QWidget, duration: int = 300, start_opacity: float = 0.0, end_opacity: float = 1.0):
        """创建淡入淡出动画"""
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(start_opacity)
        animation.setEndValue(end_opacity)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        return animation

    @staticmethod
    def create_slide_animation(widget: QWidget, start_pos: tuple, end_pos: tuple, duration: int = 300):
        """创建滑动动画"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(QRect(*start_pos))
        animation.setEndValue(QRect(*end_pos))
        animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        return animation

    @staticmethod
    def create_scale_animation(widget: QWidget, scale_factor: float = 1.05, duration: int = 150):
        """创建缩放动画"""
        # 注意：PyQt6中缩放动画需要通过几何变换实现
        original_size = widget.size()
        scaled_width = int(original_size.width() * scale_factor)
        scaled_height = int(original_size.height() * scale_factor)

        animation = QPropertyAnimation(widget, b"size")
        animation.setDuration(duration)
        animation.setStartValue(original_size)
        animation.setEndValue(widget.size().__class__(scaled_width, scaled_height))
        animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        return animation
