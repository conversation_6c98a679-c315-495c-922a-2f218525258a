# -*- coding: utf-8 -*-
"""
文档规划器模块

负责分析用户需求，生成结构化的文档创建计划，
使用DeepSeek的JSON输出模式确保返回格式的准确性。
"""

import json
import logging
from typing import Dict, Any, Optional, Callable, List
from openai import OpenAI

from prompts_config import PromptsConfig, DOCUMENT_TYPE_CONFIG


class DocumentPlanner:
    """文档规划器
    
    分析用户需求并生成详细的文档创建计划，
    使用JSON输出模式确保结构化输出。
    """
    
    def __init__(self, deepseek_client: OpenAI):
        """初始化文档规划器
        
        参数:
            deepseek_client: DeepSeek API客户端
        """
        self.client = deepseek_client
        self.logger = logging.getLogger(__name__)
        self.prompts = PromptsConfig()
    
    def analyze_user_request(self, user_input: str, 
                           callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """分析用户请求并生成文档计划
        
        参数:
            user_input: 用户输入的需求描述
            callback: 回调函数，用于流式输出
            
        返回:
            Dict: 包含文档计划的字典，如果成功则包含parsed_plan字段
        """
        try:
            if callback:
                callback('analysis', f"开始分析用户需求: {user_input}")
            
            # 构建消息
            system_prompt = self.prompts.get_document_analysis_prompt()
            user_prompt = self.prompts.format_user_prompt(user_input)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            if callback:
                callback('analysis', "正在调用DeepSeek模型进行需求分析...")
            
            # 调用DeepSeek API，使用JSON输出模式
            response = self.client.chat.completions.create(
                model="deepseek-chat",  # 使用deepseek-chat而不是deepseek-reasoner
                messages=messages,
                response_format=self.prompts.get_response_format(),
                max_tokens=8000,
                temperature=0.3  # 降低随机性，确保输出稳定
            )
            
            if callback:
                callback('analysis', "模型分析完成，正在解析结果...")
                callback('analysis', f"模型响应详情：")
                callback('analysis', f"- 模型: {response.model}")
                callback('analysis', f"- Token使用: {response.usage.total_tokens if response.usage else '未知'}")
            
            # 解析JSON响应
            content = response.choices[0].message.content
            
            # 显示原始响应内容（调试用）
            if callback:
                callback('analysis', f"🔍 DeepSeek原始响应内容:")
                callback('analysis', f"{'='*60}")
                callback('analysis', content if content else "响应内容为空")
                callback('analysis', f"{'='*60}")
            if not content:
                return {
                    "success": False,
                    "error": "DeepSeek返回了空内容，请重试"
                }
            
            try:
                parsed_plan = json.loads(content)
                if callback:
                    callback('analysis', f"成功解析文档计划，类型: {parsed_plan.get('document_type', '未知')}")
                
                # 验证和修正计划
                if callback:
                    callback('analysis', f"📋 原始解析结果验证:")
                    callback('analysis', f"- 是否包含execution_steps: {'execution_steps' in parsed_plan}")
                    if 'execution_steps' in parsed_plan:
                        for i, step in enumerate(parsed_plan['execution_steps'], 1):
                            tool_name = step.get('tool', 'unknown')
                            callback('analysis', f"  {i}. 工具: {tool_name}")
                
                # 修正可能的工具名称错误
                self._fix_tool_names(parsed_plan)
                
                # 验证和增强计划
                enhanced_plan = self._enhance_document_plan(parsed_plan, callback)
                
                # 显示增强后的计划详情（调试用）
                if callback:
                    callback('analysis', f"📋 增强后的文档计划:")
                    callback('analysis', f"- 文档类型: {enhanced_plan.get('document_type')}")
                    callback('analysis', f"- 标题: {enhanced_plan.get('title')}")
                    callback('analysis', f"- 目标字数: {enhanced_plan.get('target_words')}")
                    callback('analysis', f"- 章节数: {len(enhanced_plan.get('sections', []))}")
                    callback('analysis', f"- 执行步骤数: {len(enhanced_plan.get('execution_steps', []))}")
                    
                    # 显示执行步骤
                    callback('analysis', f"🔧 生成的执行步骤:")
                    for i, step in enumerate(enhanced_plan.get('execution_steps', []), 1):
                        callback('analysis', f"  {i}. {step.get('action')} [{step.get('tool')}]")
                
                return {
                    "success": True,
                    "raw_response": content,
                    "parsed_plan": enhanced_plan,
                    "analysis_complete": True
                }
                
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                self.logger.error(f"原始内容: {content}")
                return {
                    "success": False,
                    "error": f"JSON解析失败: {str(e)}",
                    "raw_content": content
                }
            
        except Exception as e:
            self.logger.error(f"文档分析失败: {e}")
            return {
                "success": False,
                "error": f"文档分析失败: {str(e)}"
            }
    
    def _enhance_document_plan(self, plan: Dict[str, Any], 
                             callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """增强和验证文档计划
        
        参数:
            plan: 原始文档计划
            callback: 回调函数
            
        返回:
            Dict: 增强后的文档计划
        """
        if callback:
            callback('analysis', "正在增强文档计划...")
        
        # 获取文档类型配置
        doc_type = plan.get('document_type', '')
        type_config = DOCUMENT_TYPE_CONFIG.get(doc_type, {})
        
        # 增强基本信息
        if not plan.get('target_words'):
            plan['target_words'] = type_config.get('typical_word_count', 1000)
        
        if not plan.get('file_name'):
            title = plan.get('title', '未命名文档')
            plan['file_name'] = f"{title}.docx"
        
        # 增强元数据
        if 'metadata' not in plan:
            plan['metadata'] = {}
        
        metadata = plan['metadata']
        if not metadata.get('author'):
            metadata['author'] = type_config.get('author', '文档作者')
        if not metadata.get('keywords'):
            metadata['keywords'] = type_config.get('keywords', '文档,内容')
        if not metadata.get('subject'):
            metadata['subject'] = doc_type
        if not metadata.get('description'):
            metadata['description'] = f"{doc_type} - {plan.get('title', '文档')}"
        
        # 增强章节内容
        self._enhance_sections_content(plan, callback)
        
        # 生成执行步骤
        if 'execution_steps' not in plan or not plan['execution_steps']:
            plan['execution_steps'] = self._generate_execution_steps(plan)
        
        if callback:
            sections_count = len(plan.get('sections', []))
            steps_count = len(plan.get('execution_steps', []))
            callback('analysis', f"计划增强完成: {sections_count}个章节, {steps_count}个执行步骤")
        
        return plan
    
    def _enhance_sections_content(self, plan: Dict[str, Any], 
                                callback: Callable[[str, str], None] = None):
        """增强章节内容"""
        sections = plan.get('sections', [])
        doc_type = plan.get('document_type', '')
        target_words = plan.get('target_words', 1000)
        
        if not sections:
            # 如果没有章节，根据文档类型生成默认章节
            type_config = DOCUMENT_TYPE_CONFIG.get(doc_type, {})
            default_sections = type_config.get('default_sections', ['引言', '主要内容', '总结'])
            
            sections = []
            words_per_section = target_words // len(default_sections)
            
            for i, section_title in enumerate(default_sections, 1):
                sections.append({
                    "title": f"{i}. {section_title}",
                    "content": self._generate_section_content(doc_type, section_title, words_per_section),
                    "word_count": words_per_section,
                    "style": {
                        "font_size": 14 if "概述" in section_title or "引言" in section_title else 12,
                        "bold": i == 1,  # 第一章节标题加粗
                        "alignment": "left"
                    }
                })
            
            plan['sections'] = sections
        else:
            # 增强现有章节
            total_estimated_words = sum(s.get('word_count', 0) for s in sections)
            if total_estimated_words < target_words * 0.8:  # 如果字数不足目标的80%
                # 重新分配字数
                words_per_section = target_words // len(sections)
                for section in sections:
                    if section.get('word_count', 0) < words_per_section * 0.6:
                        section['word_count'] = words_per_section
                        # 增强内容
                        if len(section.get('content', '')) < 200:  # 内容太少
                            section['content'] = self._generate_section_content(
                                doc_type, section['title'], words_per_section
                            )
    
    def _generate_section_content(self, doc_type: str, section_title: str, target_words: int) -> str:
        """生成章节内容"""
        # 使用模板生成内容
        template = self.prompts.get_content_template(doc_type, section_title)
        
        # 替换模板变量（简化版本）
        content = template.format(
            system_name="目标系统",
            section_topic=section_title.split('.')[-1].strip() if '.' in section_title else section_title,
            content_point_1="关键功能点1",
            content_point_2="关键功能点2", 
            content_point_3="关键功能点3",
            content_point_4="关键功能点4",
            detailed_description="详细描述相关概念、方法和实施过程，确保内容的完整性和专业性。",
            implementation_point_1="实施要点1：确保方案的可行性",
            implementation_point_2="实施要点2：制定详细的时间计划",
            implementation_point_3="实施要点3：建立有效的监控机制",
            notes_and_considerations="需要特别注意相关的风险因素和缓解措施，确保实施过程的顺利进行。"
        )
        
        # 如果内容长度不足，进行扩展
        current_length = len(content)
        if current_length < target_words * 0.8:  # 如果不足目标字数的80%
            additional_content = f"""

进一步说明：
本节内容涵盖了{section_title}的核心要素，通过系统性的分析和详细的说明，为读者提供全面的理解和实践指导。在实际应用中，需要根据具体情况进行适当的调整和优化。

相关标准和规范：
按照行业最佳实践和相关标准规范执行，确保质量和效果符合预期要求。同时需要考虑可扩展性和可维护性，为后续的改进和升级奠定良好基础。

实施建议：
建议采用分阶段实施的方式，先进行小范围试点，验证效果后再全面推广。在实施过程中要注重团队协作和沟通，及时解决遇到的问题和挑战。"""
            
            content += additional_content
        
        return content
    
    def _fix_tool_names(self, plan: Dict[str, Any]):
        """修正可能的工具名称错误"""
        if 'execution_steps' not in plan:
            return
        
        # 工具名称映射表（修正常见错误）
        tool_name_mapping = {
            'add_table': 'create_table',
            'insert_table': 'create_table',
            'make_table': 'create_table',
            'new_table': 'create_table',
            'add_content': 'add_all_content',
            'insert_content': 'add_all_content',
            'write_text': 'add_text',
            'insert_text': 'add_text',
            'new_document': 'create_document',
            'make_document': 'create_document',
            'save_file': 'save_document',
            'export_document': 'save_document'
        }
        
        for step in plan['execution_steps']:
            original_tool = step.get('tool', '')
            if original_tool in tool_name_mapping:
                corrected_tool = tool_name_mapping[original_tool]
                step['tool'] = corrected_tool
                step['action'] = step['action'].replace(original_tool, corrected_tool)
    
    def _generate_execution_steps(self, plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成执行步骤"""
        steps = []
        step_num = 1
        
        # 步骤1：创建文档
        steps.append({
            "step": step_num,
            "action": "创建空白文档",
            "tool": "create_document",
            "parameters": {
                "title": plan.get('title', '未命名文档')
            }
        })
        step_num += 1
        
        # 步骤2：设置元数据
        if plan.get('metadata'):
            steps.append({
                "step": step_num,
                "action": "设置文档元数据",
                "tool": "set_document_metadata",
                "parameters": plan['metadata']
            })
            step_num += 1
        
        # 步骤3：一次性添加所有章节内容
        sections = plan.get('sections', [])
        if sections:
            # 将所有章节内容合并为一个文本
            all_content = ""
            for section in sections:
                section_title = section['title']
                section_content = section['content']
                all_content += f"\n{section_title}\n{section_content}\n\n"
            
            steps.append({
                "step": step_num,
                "action": "添加所有章节内容",
                "tool": "add_all_content",
                "parameters": {
                    "content": all_content.strip(),
                    "sections": sections  # 保留章节信息用于样式处理
                }
            })
            step_num += 1
        
        # 步骤4：创建表格（如果有）
        tables = plan.get('tables', [])
        for table in tables:
            steps.append({
                "step": step_num,
                "action": f"创建表格: {table.get('caption', '表格')}",
                "tool": "create_table",  # 确保工具名称正确
                "parameters": {
                    "rows": table.get('rows', 3),
                    "cols": table.get('cols', 3),
                    "data": table.get('data', []),
                    "caption": table.get('caption')
                }
            })
            step_num += 1
        
        # 步骤5：保存文档
        steps.append({
            "step": step_num,
            "action": "保存文档",
            "tool": "save_document",
            "parameters": {
                "file_path": plan.get('file_name', '文档.docx'),
                "format_type": "docx"
            }
        })
        
        return steps
    
    def validate_document_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """验证文档计划的完整性和合理性
        
        参数:
            plan: 文档计划字典
            
        返回:
            Dict: 验证结果，包含is_valid和issues字段
        """
        issues = []
        
        # 检查必需字段
        required_fields = ['document_type', 'title', 'target_words', 'sections', 'execution_steps']
        for field in required_fields:
            if field not in plan:
                issues.append(f"缺少必需字段: {field}")
        
        # 检查章节结构
        sections = plan.get('sections', [])
        if not sections:
            issues.append("文档必须包含至少一个章节")
        else:
            total_words = sum(s.get('word_count', 0) for s in sections)
            target_words = plan.get('target_words', 0)
            if total_words < target_words * 0.7:  # 低于目标字数70%
                issues.append(f"章节总字数({total_words})低于目标字数({target_words})的70%")
        
        # 检查执行步骤
        steps = plan.get('execution_steps', [])
        if not steps:
            issues.append("缺少执行步骤")
        else:
            # 检查是否包含基本步骤
            step_tools = [step.get('tool') for step in steps]
            if 'create_document' not in step_tools:
                issues.append("执行步骤中缺少创建文档步骤")
            if 'save_document' not in step_tools:
                issues.append("执行步骤中缺少保存文档步骤")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "suggestions": self._generate_fix_suggestions(issues) if issues else []
        }
    
    def _generate_fix_suggestions(self, issues: List[str]) -> List[str]:
        """生成修复建议"""
        suggestions = []
        for issue in issues:
            if "缺少必需字段" in issue:
                suggestions.append("请补充完整的文档计划信息")
            elif "章节总字数" in issue:
                suggestions.append("建议增加章节内容或添加新章节")
            elif "缺少执行步骤" in issue:
                suggestions.append("请添加完整的文档创建和保存步骤")
        return suggestions


class StreamingDocumentPlanner(DocumentPlanner):
    """支持流式输出的文档规划器"""
    
    def analyze_user_request_stream(self, user_input: str, 
                                  callback: Callable[[str, str], None] = None) -> Dict[str, Any]:
        """流式分析用户请求
        
        与基础版本类似，但提供更详细的进度反馈
        """
        try:
            if callback:
                callback('reasoning', "🔍 开始深度分析用户需求...")
                callback('reasoning', f"用户输入: {user_input}")
                callback('reasoning', "正在识别文档类型和结构要求...")
            
            # 分析文档类型
            detected_type = self._detect_document_type(user_input)
            if callback:
                callback('reasoning', f"检测到文档类型: {detected_type}")
            
            # 提取字数要求
            target_words = self._extract_word_count(user_input)
            if callback:
                callback('reasoning', f"目标字数: {target_words}")
            
            # 调用基础分析方法
            result = self.analyze_user_request(user_input, callback)
            
            if result.get('success') and callback:
                plan = result['parsed_plan']
                callback('reasoning', f"✅ 分析完成！生成了包含{len(plan.get('sections', []))}个章节的详细计划")
            
            return result
            
        except Exception as e:
            if callback:
                callback('reasoning', f"❌ 分析过程中出现错误: {str(e)}")
            return {
                "success": False,
                "error": f"流式分析失败: {str(e)}"
            }
    
    def _detect_document_type(self, user_input: str) -> str:
        """检测文档类型"""
        input_lower = user_input.lower()
        
        if any(keyword in input_lower for keyword in ['测试', 'test', '验证', '质量']):
            return "软件测试文档"
        elif any(keyword in input_lower for keyword in ['技术', '规范', '需求', 'spec']):
            return "技术规范文档"
        elif any(keyword in input_lower for keyword in ['用户', '手册', '指南', 'manual']):
            return "用户手册"
        elif any(keyword in input_lower for keyword in ['项目', '计划', 'project', 'plan']):
            return "项目计划书"
        else:
            return "通用文档"
    
    def _extract_word_count(self, user_input: str) -> int:
        """提取字数要求"""
        import re
        
        # 匹配数字+字的模式
        patterns = [
            r'(\d+)\s*字',
            r'(\d+)\s*words?',
            r'字数[：:]\s*(\d+)',
            r'要求\s*(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        # 默认返回1000字
        return 1000 