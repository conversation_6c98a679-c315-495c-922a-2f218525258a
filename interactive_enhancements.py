# -*- coding: utf-8 -*-
"""
交互增强模块 - 为PyQt6界面添加现代化交互效果
包含动画、过渡效果、视觉反馈等功能
"""

from typing import Optional, Callable, List
from PyQt6.QtWidgets import (
    QWidget, QPushButton, QFrame, QLabel, QGraphicsOpacityEffect,
    QGraphicsDropShadowEffect, QApplication, QHBoxLayout, QVBoxLayout
)
from PyQt6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QParallelAnimationGroup,
    QSequentialAnimationGroup, QTimer, QRect, QSize, pyqtSignal,
    QObject, QThread
)
from PyQt6.QtGui import QColor, QPalette

from ui_design_system import ModernDesignSystem


class AnimationManager(QObject):
    """动画管理器"""
    
    def __init__(self):
        super().__init__()
        self.active_animations = []
    
    def create_fade_in_animation(self, widget: QWidget, duration: int = 300) -> QPropertyAnimation:
        """创建淡入动画"""
        # 创建透明度效果
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        # 创建动画
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        
        self.active_animations.append(animation)
        animation.finished.connect(lambda: self.active_animations.remove(animation))
        
        return animation
    
    def create_slide_in_animation(self, widget: QWidget, direction: str = "left", 
                                duration: int = 400) -> QPropertyAnimation:
        """创建滑入动画"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 获取目标位置
        target_rect = widget.geometry()
        
        # 设置起始位置
        if direction == "left":
            start_rect = QRect(target_rect.x() - target_rect.width(), target_rect.y(),
                             target_rect.width(), target_rect.height())
        elif direction == "right":
            start_rect = QRect(target_rect.x() + target_rect.width(), target_rect.y(),
                             target_rect.width(), target_rect.height())
        elif direction == "top":
            start_rect = QRect(target_rect.x(), target_rect.y() - target_rect.height(),
                             target_rect.width(), target_rect.height())
        else:  # bottom
            start_rect = QRect(target_rect.x(), target_rect.y() + target_rect.height(),
                             target_rect.width(), target_rect.height())
        
        animation.setStartValue(start_rect)
        animation.setEndValue(target_rect)
        
        self.active_animations.append(animation)
        animation.finished.connect(lambda: self.active_animations.remove(animation))
        
        return animation
    
    def create_bounce_animation(self, widget: QWidget, scale_factor: float = 1.1, 
                              duration: int = 200) -> QSequentialAnimationGroup:
        """创建弹跳动画"""
        # 获取原始大小
        original_size = widget.size()
        scaled_size = QSize(int(original_size.width() * scale_factor),
                          int(original_size.height() * scale_factor))
        
        # 放大动画
        scale_up = QPropertyAnimation(widget, b"size")
        scale_up.setDuration(duration // 2)
        scale_up.setStartValue(original_size)
        scale_up.setEndValue(scaled_size)
        scale_up.setEasingCurve(QEasingCurve.Type.OutQuad)
        
        # 缩小动画
        scale_down = QPropertyAnimation(widget, b"size")
        scale_down.setDuration(duration // 2)
        scale_down.setStartValue(scaled_size)
        scale_down.setEndValue(original_size)
        scale_down.setEasingCurve(QEasingCurve.Type.InQuad)
        
        # 组合动画
        bounce_group = QSequentialAnimationGroup()
        bounce_group.addAnimation(scale_up)
        bounce_group.addAnimation(scale_down)
        
        self.active_animations.append(bounce_group)
        bounce_group.finished.connect(lambda: self.active_animations.remove(bounce_group))
        
        return bounce_group
    
    def create_pulse_animation(self, widget: QWidget, duration: int = 1000) -> QPropertyAnimation:
        """创建脉冲动画（循环）"""
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(1.0)
        animation.setEndValue(0.3)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        animation.setLoopCount(-1)  # 无限循环
        
        # 设置往返动画
        animation.setDirection(QPropertyAnimation.Direction.Forward)
        
        self.active_animations.append(animation)
        
        return animation
    
    def stop_all_animations(self):
        """停止所有动画"""
        for animation in self.active_animations:
            animation.stop()
        self.active_animations.clear()


class InteractiveButton(QPushButton):
    """增强的交互式按钮"""
    
    def __init__(self, text: str = "", button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.animation_manager = AnimationManager()
        self.is_processing = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 根据按钮类型设置样式
        if self.button_type == "primary":
            self.setStyleSheet(f"""
                InteractiveButton {{
                    background-color: {ModernDesignSystem.get_color('primary.main')};
                    color: white;
                    border: none;
                    padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('lg')}px;
                    border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                    font-weight: 600;
                    font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                }}
                InteractiveButton:hover {{
                    background-color: {ModernDesignSystem.get_color('primary.light')};
                }}
                InteractiveButton:pressed {{
                    background-color: {ModernDesignSystem.get_color('primary.dark')};
                }}
                InteractiveButton:disabled {{
                    background-color: {ModernDesignSystem.get_color('neutral.gray_300')};
                    color: {ModernDesignSystem.get_color('neutral.gray_500')};
                }}
            """)
        elif self.button_type == "secondary":
            self.setStyleSheet(f"""
                InteractiveButton {{
                    background-color: transparent;
                    color: {ModernDesignSystem.get_color('primary.main')};
                    border: 2px solid {ModernDesignSystem.get_color('primary.main')};
                    padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('lg')}px;
                    border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                    font-weight: 600;
                    font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                }}
                InteractiveButton:hover {{
                    background-color: {ModernDesignSystem.get_color('primary.main')};
                    color: white;
                }}
            """)
        elif self.button_type == "success":
            self.setStyleSheet(f"""
                InteractiveButton {{
                    background-color: {ModernDesignSystem.get_color('success.main')};
                    color: white;
                    border: none;
                    padding: {ModernDesignSystem.get_spacing('sm')}px {ModernDesignSystem.get_spacing('lg')}px;
                    border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                    font-weight: 600;
                    font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                }}
                InteractiveButton:hover {{
                    background-color: {ModernDesignSystem.get_color('success.light')};
                }}
            """)
        
        # 添加阴影效果
        shadow_effect = ModernDesignSystem.create_shadow_effect('sm')
        self.setGraphicsEffect(shadow_effect)
    
    def setup_animations(self):
        """设置动画"""
        # 悬停动画
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(150)
        self.hover_animation.setEasingCurve(QEasingCurve.Type.OutQuad)
        
        # 点击动画
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)
        self.click_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        if not self.is_processing:
            # 轻微上移
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x(), current_rect.y() - 2,
                              current_rect.width(), current_rect.height())
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(target_rect)
            self.hover_animation.start()
            
            # 更新阴影
            shadow_effect = ModernDesignSystem.create_shadow_effect('md')
            self.setGraphicsEffect(shadow_effect)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        if not self.is_processing:
            # 恢复位置
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x(), current_rect.y() + 2,
                              current_rect.width(), current_rect.height())
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(target_rect)
            self.hover_animation.start()
            
            # 恢复阴影
            shadow_effect = ModernDesignSystem.create_shadow_effect('sm')
            self.setGraphicsEffect(shadow_effect)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        super().mousePressEvent(event)
        if not self.is_processing:
            # 轻微缩放
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() + 1, current_rect.y() + 1,
                              current_rect.width() - 2, current_rect.height() - 2)
            
            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(target_rect)
            self.click_animation.start()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        super().mouseReleaseEvent(event)
        if not self.is_processing:
            # 恢复大小
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() - 1, current_rect.y() - 1,
                              current_rect.width() + 2, current_rect.height() + 2)
            
            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(target_rect)
            self.click_animation.start()
    
    def set_processing(self, processing: bool):
        """设置处理状态"""
        self.is_processing = processing
        if processing:
            self.setText("处理中...")
            self.setEnabled(False)
            # 开始脉冲动画
            self.pulse_animation = self.animation_manager.create_pulse_animation(self)
            self.pulse_animation.start()
        else:
            self.setEnabled(True)
            if hasattr(self, 'pulse_animation'):
                self.pulse_animation.stop()


class LoadingIndicator(QLabel):
    """加载指示器"""
    
    def __init__(self, size: int = 32, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self.setup_ui()
        self.setup_animation()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(self.size, self.size)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setText("⟳")
        self.setFont(ModernDesignSystem.get_font('xl', 'normal'))
        self.setStyleSheet(f"color: {ModernDesignSystem.get_color('primary.main')};")
    
    def setup_animation(self):
        """设置旋转动画"""
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.rotate)
        self.rotation_timer.setInterval(50)  # 50ms间隔
    
    def start_loading(self):
        """开始加载动画"""
        self.rotation_timer.start()
        self.show()
    
    def stop_loading(self):
        """停止加载动画"""
        self.rotation_timer.stop()
        self.hide()
    
    def rotate(self):
        """旋转文本"""
        self.angle = (self.angle + 10) % 360
        # 简单的旋转效果，通过改变文本实现
        rotation_chars = ["⟳", "⟲", "⟳", "⟲"]
        char_index = (self.angle // 90) % len(rotation_chars)
        self.setText(rotation_chars[char_index])


class NotificationToast(QFrame):
    """通知提示框"""
    
    def __init__(self, message: str, toast_type: str = "info", duration: int = 3000, parent=None):
        super().__init__(parent)
        self.message = message
        self.toast_type = toast_type
        self.duration = duration
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(60)
        self.setMinimumWidth(300)
        
        # 根据类型设置样式
        if self.toast_type == "success":
            bg_color = ModernDesignSystem.get_color('success.surface')
            border_color = ModernDesignSystem.get_color('success.main')
            text_color = ModernDesignSystem.get_color('success.dark')
            icon = "✓"
        elif self.toast_type == "warning":
            bg_color = ModernDesignSystem.get_color('warning.surface')
            border_color = ModernDesignSystem.get_color('warning.main')
            text_color = ModernDesignSystem.get_color('warning.dark')
            icon = "⚠"
        elif self.toast_type == "error":
            bg_color = ModernDesignSystem.get_color('error.surface')
            border_color = ModernDesignSystem.get_color('error.main')
            text_color = ModernDesignSystem.get_color('error.dark')
            icon = "✗"
        else:  # info
            bg_color = ModernDesignSystem.get_color('primary.surface')
            border_color = ModernDesignSystem.get_color('primary.main')
            text_color = ModernDesignSystem.get_color('primary.dark')
            icon = "ℹ"
        
        self.setStyleSheet(f"""
            NotificationToast {{
                background-color: {bg_color};
                border-left: 4px solid {border_color};
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                padding: {ModernDesignSystem.get_spacing('md')}px;
            }}
        """)
        
        # 布局
        layout = QHBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('sm'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('sm')
        )
        self.setLayout(layout)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setFont(ModernDesignSystem.get_font('lg', 'bold'))
        icon_label.setStyleSheet(f"color: {text_color};")
        layout.addWidget(icon_label)
        
        # 消息文本
        message_label = QLabel(self.message)
        message_label.setFont(ModernDesignSystem.get_font('sm', 'medium'))
        message_label.setStyleSheet(f"color: {text_color};")
        message_label.setWordWrap(True)
        layout.addWidget(message_label, 1)
        
        # 添加阴影
        shadow_effect = ModernDesignSystem.create_shadow_effect('lg')
        self.setGraphicsEffect(shadow_effect)
    
    def setup_animations(self):
        """设置动画"""
        self.animation_manager = AnimationManager()
    
    def show_toast(self):
        """显示提示框"""
        # 淡入动画
        fade_in = self.animation_manager.create_fade_in_animation(self, 300)
        fade_in.start()
        
        self.show()
        
        # 自动隐藏
        QTimer.singleShot(self.duration, self.hide_toast)
    
    def hide_toast(self):
        """隐藏提示框"""
        # 淡出动画
        opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(opacity_effect)
        
        fade_out = QPropertyAnimation(opacity_effect, b"opacity")
        fade_out.setDuration(300)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QEasingCurve.Type.InQuad)
        fade_out.finished.connect(self.hide)
        fade_out.start()
