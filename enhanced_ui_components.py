# -*- coding: utf-8 -*-
"""
增强版UI组件 - 基于现代设计系统的PyQt6组件
包含改进的卡片、聊天气泡、导航按钮等组件
"""

import sys
from typing import Optional, Callable
from PyQt6.QtWidgets import (
    QFrame, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QGraphicsDropShadowEffect, QSizePolicy, QSpacerItem
)
from PyQt6.QtCore import (
    Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, 
    pyqtSignal, QSize, QParallelAnimationGroup
)
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen

from ui_design_system import ModernDesignSystem, ModernStyleSheets, AnimationHelper


class EnhancedModernCard(QFrame):
    """增强版现代化卡片组件"""
    
    clicked = pyqtSignal(str)  # 点击信号，传递卡片标题
    
    def __init__(self, title: str, icon: str = "", description: str = "", 
                 action_text: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.description = description
        self.action_text = action_text
        self.is_hovered = False
        self.is_pressed = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(220, 140)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置基础样式
        self.setStyleSheet(f"""
            EnhancedModernCard {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
            }}
        """)
        
        # 添加阴影效果
        self.shadow_effect = ModernDesignSystem.create_shadow_effect('sm')
        self.setGraphicsEffect(self.shadow_effect)
        
        # 主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('lg')
        )
        layout.setSpacing(ModernDesignSystem.get_spacing('sm'))
        self.setLayout(layout)
        
        # 顶部区域（图标和标题）
        top_layout = QHBoxLayout()
        top_layout.setSpacing(ModernDesignSystem.get_spacing('sm'))
        
        # 图标
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setFont(ModernDesignSystem.get_font('xl', 'normal'))
            icon_label.setFixedSize(32, 32)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            top_layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setFont(ModernDesignSystem.get_font('base', 'semibold'))
        title_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_800')};")
        title_label.setWordWrap(True)
        top_layout.addWidget(title_label, 1)
        
        layout.addLayout(top_layout)
        
        # 描述文本
        if self.description:
            desc_label = QLabel(self.description)
            desc_label.setFont(ModernDesignSystem.get_font('sm', 'normal'))
            desc_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
            desc_label.setWordWrap(True)
            desc_label.setAlignment(Qt.AlignmentFlag.AlignTop)
            layout.addWidget(desc_label, 1)
        
        # 底部操作区域
        if self.action_text:
            action_layout = QHBoxLayout()
            action_layout.addStretch()
            
            action_label = QLabel(self.action_text)
            action_label.setFont(ModernDesignSystem.get_font('sm', 'medium'))
            action_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('primary.main')};")
            action_layout.addWidget(action_label)
            
            # 箭头图标
            arrow_label = QLabel("→")
            arrow_label.setFont(ModernDesignSystem.get_font('sm', 'bold'))
            arrow_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('primary.main')};")
            action_layout.addWidget(arrow_label)
            
            layout.addLayout(action_layout)
        else:
            layout.addStretch()
    
    def setup_animations(self):
        """设置动画"""
        # 悬停动画
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(ModernDesignSystem.ANIMATIONS['duration']['fast'])
        self.hover_animation.setEasingCurve(ModernDesignSystem.ANIMATIONS['easing']['ease_out'])
        
        # 点击动画
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(ModernDesignSystem.ANIMATIONS['duration']['fast'])
        self.click_animation.setEasingCurve(ModernDesignSystem.ANIMATIONS['easing']['ease_in_out'])
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        self.is_hovered = True
        
        # 更新样式
        self.setStyleSheet(f"""
            EnhancedModernCard {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('primary.main')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
            }}
        """)
        
        # 更新阴影
        self.shadow_effect = ModernDesignSystem.create_shadow_effect('md')
        self.setGraphicsEffect(self.shadow_effect)
        
        # 轻微上移动画
        current_rect = self.geometry()
        target_rect = QRect(current_rect.x(), current_rect.y() - 2, 
                          current_rect.width(), current_rect.height())
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(target_rect)
        self.hover_animation.start()
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        self.is_hovered = False
        
        # 恢复样式
        self.setStyleSheet(f"""
            EnhancedModernCard {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
            }}
        """)
        
        # 恢复阴影
        self.shadow_effect = ModernDesignSystem.create_shadow_effect('sm')
        self.setGraphicsEffect(self.shadow_effect)
        
        # 恢复位置动画
        current_rect = self.geometry()
        target_rect = QRect(current_rect.x(), current_rect.y() + 2, 
                          current_rect.width(), current_rect.height())
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(target_rect)
        self.hover_animation.start()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_pressed = True
            
            # 按下样式
            self.setStyleSheet(f"""
                EnhancedModernCard {{
                    background-color: {ModernDesignSystem.get_color('primary.surface')};
                    border: 2px solid {ModernDesignSystem.get_color('primary.main')};
                    border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                }}
            """)
            
            # 按下动画（轻微缩放）
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() + 1, current_rect.y() + 1, 
                              current_rect.width() - 2, current_rect.height() - 2)
            
            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(target_rect)
            self.click_animation.start()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.is_pressed:
            self.is_pressed = False
            
            # 恢复样式
            if self.is_hovered:
                self.setStyleSheet(f"""
                    EnhancedModernCard {{
                        background-color: {ModernDesignSystem.get_color('background.primary')};
                        border: 1px solid {ModernDesignSystem.get_color('primary.main')};
                        border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                    }}
                """)
            else:
                self.setStyleSheet(f"""
                    EnhancedModernCard {{
                        background-color: {ModernDesignSystem.get_color('background.primary')};
                        border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                        border-radius: {ModernDesignSystem.get_border_radius('lg')}px;
                    }}
                """)
            
            # 恢复大小动画
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() - 1, current_rect.y() - 1, 
                              current_rect.width() + 2, current_rect.height() + 2)
            
            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(target_rect)
            self.click_animation.start()
            
            # 发射点击信号
            self.clicked.emit(self.title)


class EnhancedChatMessage(QFrame):
    """增强版聊天消息组件"""
    
    def __init__(self, message: str, is_user: bool = True, message_type: str = "text", 
                 timestamp: str = "", parent=None):
        super().__init__(parent)
        self.message = message
        self.is_user = is_user
        self.message_type = message_type
        self.timestamp = timestamp
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('sm'),
            ModernDesignSystem.get_spacing('lg'),
            ModernDesignSystem.get_spacing('sm')
        )
        self.setLayout(main_layout)
        
        # 消息容器
        message_container = QFrame()
        message_container.setMaximumWidth(600)  # 限制最大宽度
        
        # 根据发送者设置样式和对齐
        if self.is_user:
            message_container.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {ModernDesignSystem.get_color('primary.main')}, 
                        stop:1 {ModernDesignSystem.get_color('primary.light')});
                    color: white;
                    border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                    padding: {ModernDesignSystem.get_spacing('md')}px {ModernDesignSystem.get_spacing('lg')}px;
                }}
            """)
            main_layout.addStretch()  # 右对齐
            main_layout.addWidget(message_container)
        else:
            message_container.setStyleSheet(f"""
                QFrame {{
                    background-color: {ModernDesignSystem.get_color('background.primary')};
                    color: {ModernDesignSystem.get_color('neutral.gray_800')};
                    border: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
                    border-radius: {ModernDesignSystem.get_border_radius('xl')}px;
                    padding: {ModernDesignSystem.get_spacing('md')}px {ModernDesignSystem.get_spacing('lg')}px;
                }}
            """)
            main_layout.addWidget(message_container)
            main_layout.addStretch()  # 左对齐
        
        # 消息内容布局
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(ModernDesignSystem.get_spacing('xs'))
        message_container.setLayout(content_layout)
        
        # 消息文本
        message_label = QLabel(self.message)
        message_label.setFont(ModernDesignSystem.get_font('sm', 'normal'))
        message_label.setWordWrap(True)
        message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        content_layout.addWidget(message_label)
        
        # 时间戳（如果提供）
        if self.timestamp:
            timestamp_label = QLabel(self.timestamp)
            timestamp_label.setFont(ModernDesignSystem.get_font('xs', 'normal'))
            if self.is_user:
                timestamp_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
            else:
                timestamp_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_500')};")
            timestamp_label.setAlignment(Qt.AlignmentFlag.AlignRight if self.is_user else Qt.AlignmentFlag.AlignLeft)
            content_layout.addWidget(timestamp_label)
        
        # 添加阴影效果
        shadow_effect = ModernDesignSystem.create_shadow_effect('sm')
        message_container.setGraphicsEffect(shadow_effect)
    
    def setup_animations(self):
        """设置动画"""
        # 淡入动画
        self.fade_animation = AnimationHelper.create_fade_animation(self, 300, 0.0, 1.0)
        
        # 启动淡入动画
        QTimer.singleShot(50, self.fade_animation.start)


class EnhancedNavigationButton(QPushButton):
    """增强版导航按钮"""

    def __init__(self, func_id: str, icon: str, title: str, description: str, parent=None):
        super().__init__(parent)
        self.func_id = func_id
        self.icon = icon
        self.title = title
        self.description = description

        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """设置UI"""
        self.setCheckable(True)
        self.setFixedHeight(70)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        # 设置基础样式
        self.setStyleSheet(f"""
            EnhancedNavigationButton {{
                background: transparent;
                border: none;
                text-align: left;
                padding: {ModernDesignSystem.get_spacing('md')}px;
                margin: 2px {ModernDesignSystem.get_spacing('md')}px;
                border-radius: {ModernDesignSystem.get_border_radius('md')}px;
                font-size: {ModernDesignSystem.TYPOGRAPHY['font_sizes']['sm']}px;
                color: {ModernDesignSystem.get_color('neutral.gray_700')};
            }}
            EnhancedNavigationButton:hover {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_100')};
            }}
            EnhancedNavigationButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernDesignSystem.get_color('primary.surface')},
                    stop:1 {ModernDesignSystem.get_color('primary.surface')});
                border-left: 3px solid {ModernDesignSystem.get_color('primary.main')};
                color: {ModernDesignSystem.get_color('primary.main')};
                font-weight: 600;
            }}
        """)

        # 创建自定义布局
        self.update_text()

    def update_text(self):
        """更新按钮文本"""
        text = f"{self.icon}  {self.title}\n    {self.description}"
        self.setText(text)

    def setup_animations(self):
        """设置动画"""
        # 悬停动画
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(ModernDesignSystem.ANIMATIONS['duration']['fast'])
        self.hover_animation.setEasingCurve(ModernDesignSystem.ANIMATIONS['easing']['ease_out'])

    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        if not self.isChecked():
            # 轻微缩放效果
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() - 2, current_rect.y(),
                              current_rect.width() + 4, current_rect.height())

            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(target_rect)
            self.hover_animation.start()

    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        if not self.isChecked():
            # 恢复大小
            current_rect = self.geometry()
            target_rect = QRect(current_rect.x() + 2, current_rect.y(),
                              current_rect.width() - 4, current_rect.height())

            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(target_rect)
            self.hover_animation.start()


class EnhancedProgressIndicator(QWidget):
    """增强版进度指示器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.progress_value = 0
        self.is_indeterminate = False
        self.animation_value = 0

        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(4)
        self.setStyleSheet(f"""
            EnhancedProgressIndicator {{
                background-color: {ModernDesignSystem.get_color('neutral.gray_200')};
                border-radius: 2px;
            }}
        """)

    def setup_animations(self):
        """设置动画"""
        # 不确定进度动画
        self.indeterminate_animation = QPropertyAnimation(self, b"animation_value")
        self.indeterminate_animation.setDuration(2000)
        self.indeterminate_animation.setStartValue(0)
        self.indeterminate_animation.setEndValue(100)
        self.indeterminate_animation.setLoopCount(-1)  # 无限循环
        self.indeterminate_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        self.indeterminate_animation.valueChanged.connect(self.update)

    def set_progress(self, value: int):
        """设置进度值"""
        self.progress_value = max(0, min(100, value))
        self.is_indeterminate = False
        self.indeterminate_animation.stop()
        self.update()

    def set_indeterminate(self, indeterminate: bool):
        """设置不确定进度模式"""
        self.is_indeterminate = indeterminate
        if indeterminate:
            self.indeterminate_animation.start()
        else:
            self.indeterminate_animation.stop()
        self.update()

    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景
        bg_color = QColor(ModernDesignSystem.get_color('neutral.gray_200'))
        painter.fillRect(self.rect(), bg_color)

        # 绘制进度
        if self.is_indeterminate:
            # 不确定进度动画
            progress_color = QColor(ModernDesignSystem.get_color('primary.main'))
            progress_width = self.width() * 0.3  # 30%宽度的进度条
            progress_x = (self.width() - progress_width) * (self.animation_value / 100)

            progress_rect = QRect(int(progress_x), 0, int(progress_width), self.height())
            painter.fillRect(progress_rect, progress_color)
        else:
            # 确定进度
            progress_color = QColor(ModernDesignSystem.get_color('primary.main'))
            progress_width = self.width() * (self.progress_value / 100)

            progress_rect = QRect(0, 0, int(progress_width), self.height())
            painter.fillRect(progress_rect, progress_color)


class EnhancedStatusBar(QWidget):
    """增强版状态栏"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(32)
        self.setStyleSheet(f"""
            EnhancedStatusBar {{
                background-color: {ModernDesignSystem.get_color('background.primary')};
                border-top: 1px solid {ModernDesignSystem.get_color('neutral.gray_200')};
            }}
        """)

        # 布局
        layout = QHBoxLayout()
        layout.setContentsMargins(
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('xs'),
            ModernDesignSystem.get_spacing('md'),
            ModernDesignSystem.get_spacing('xs')
        )
        self.setLayout(layout)

        # 状态文本
        self.status_label = QLabel("就绪")
        self.status_label.setFont(ModernDesignSystem.get_font('sm', 'normal'))
        self.status_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
        layout.addWidget(self.status_label)

        layout.addStretch()

        # 连接状态指示器
        self.connection_indicator = QLabel("●")
        self.connection_indicator.setFont(ModernDesignSystem.get_font('sm', 'bold'))
        self.set_connection_status(True)  # 默认连接状态
        layout.addWidget(self.connection_indicator)

        # 连接状态文本
        self.connection_label = QLabel("已连接")
        self.connection_label.setFont(ModernDesignSystem.get_font('sm', 'normal'))
        self.connection_label.setStyleSheet(f"color: {ModernDesignSystem.get_color('neutral.gray_600')};")
        layout.addWidget(self.connection_label)

    def set_status(self, message: str, message_type: str = "info"):
        """设置状态消息"""
        self.status_label.setText(message)

        # 根据消息类型设置颜色
        if message_type == "success":
            color = ModernDesignSystem.get_color('success.main')
        elif message_type == "warning":
            color = ModernDesignSystem.get_color('warning.main')
        elif message_type == "error":
            color = ModernDesignSystem.get_color('error.main')
        else:
            color = ModernDesignSystem.get_color('neutral.gray_600')

        self.status_label.setStyleSheet(f"color: {color};")

    def set_connection_status(self, connected: bool):
        """设置连接状态"""
        if connected:
            self.connection_indicator.setStyleSheet(f"color: {ModernDesignSystem.get_color('success.main')};")
            self.connection_label.setText("已连接")
        else:
            self.connection_indicator.setStyleSheet(f"color: {ModernDesignSystem.get_color('error.main')};")
            self.connection_label.setText("未连接")
