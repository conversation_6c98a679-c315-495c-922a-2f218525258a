# -*- coding: utf-8 -*-
"""
无障碍访问增强模块 - 提升PyQt6界面的可访问性
包含键盘导航、屏幕阅读器支持、对比度优化等功能
"""

from typing import Dict, List, Optional, Callable
from PyQt6.QtWidgets import (
    QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox,
    QCheckBox, QRadioButton, QSlider, QProgressBar, QTabWidget,
    QApplication, QToolTip, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QEvent
from PyQt6.QtGui import (
    QKeySequence, QShortcut, QFont, QColor, QPalette, QFocusEvent,
    QKeyEvent, QAccessible, QAccessibleInterface
)

from ui_design_system import ModernDesignSystem


class AccessibilityManager(QObject):
    """无障碍访问管理器"""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.focus_indicators = {}
        self.keyboard_shortcuts = {}
        self.screen_reader_announcements = []
        
        self.setup_accessibility_features()
    
    def setup_accessibility_features(self):
        """设置无障碍功能"""
        self.setup_keyboard_navigation()
        self.setup_focus_indicators()
        self.setup_screen_reader_support()
        self.setup_high_contrast_mode()
    
    def setup_keyboard_navigation(self):
        """设置键盘导航"""
        # 全局快捷键
        shortcuts = {
            'Ctrl+1': lambda: self.navigate_to_section('chat'),
            'Ctrl+2': lambda: self.navigate_to_section('document'),
            'Ctrl+3': lambda: self.navigate_to_section('data'),
            'Ctrl+4': lambda: self.navigate_to_section('email'),
            'Ctrl+N': lambda: self.trigger_new_action(),
            'Ctrl+S': lambda: self.trigger_save_action(),
            'F1': lambda: self.show_help(),
            'Escape': lambda: self.close_current_dialog(),
            'Alt+F4': lambda: self.main_window.close(),
        }
        
        for key_sequence, callback in shortcuts.items():
            shortcut = QShortcut(QKeySequence(key_sequence), self.main_window)
            shortcut.activated.connect(callback)
            self.keyboard_shortcuts[key_sequence] = shortcut
    
    def setup_focus_indicators(self):
        """设置焦点指示器"""
        # 为所有可聚焦控件添加焦点样式
        focus_style = f"""
            *:focus {{
                outline: 2px solid {ModernDesignSystem.get_color('primary.main')};
                outline-offset: 2px;
                border-radius: {ModernDesignSystem.get_border_radius('sm')}px;
            }}
        """
        
        # 应用焦点样式
        current_style = self.main_window.styleSheet()
        self.main_window.setStyleSheet(current_style + focus_style)
    
    def setup_screen_reader_support(self):
        """设置屏幕阅读器支持"""
        # 启用无障碍功能
        QApplication.instance().setAttribute(Qt.ApplicationAttribute.AA_SynthesizeMouseForUnhandledTabletEvents, True)
        
        # 为主要控件设置无障碍属性
        self.setup_accessible_names()
    
    def setup_accessible_names(self):
        """设置无障碍名称和描述"""
        # 这个方法需要在UI创建后调用
        def set_accessible_properties(widget, name, description=""):
            if widget:
                widget.setAccessibleName(name)
                if description:
                    widget.setAccessibleDescription(description)
                widget.setToolTip(description if description else name)
        
        # 存储设置函数，稍后调用
        self.set_accessible_properties = set_accessible_properties
    
    def setup_high_contrast_mode(self):
        """设置高对比度模式"""
        # 检测系统高对比度设置
        palette = QApplication.instance().palette()
        if self.is_high_contrast_mode():
            self.apply_high_contrast_theme()
    
    def is_high_contrast_mode(self) -> bool:
        """检测是否为高对比度模式"""
        # 简单的检测方法，实际应用中可能需要更复杂的逻辑
        palette = QApplication.instance().palette()
        bg_color = palette.color(QPalette.ColorRole.Window)
        text_color = palette.color(QPalette.ColorRole.WindowText)
        
        # 计算对比度
        contrast_ratio = self.calculate_contrast_ratio(bg_color, text_color)
        return contrast_ratio > 7.0  # WCAG AAA 标准
    
    def calculate_contrast_ratio(self, color1: QColor, color2: QColor) -> float:
        """计算两个颜色的对比度"""
        def get_relative_luminance(color: QColor) -> float:
            def linearize(value):
                value = value / 255.0
                if value <= 0.03928:
                    return value / 12.92
                else:
                    return pow((value + 0.055) / 1.055, 2.4)
            
            r = linearize(color.red())
            g = linearize(color.green())
            b = linearize(color.blue())
            
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        l1 = get_relative_luminance(color1)
        l2 = get_relative_luminance(color2)
        
        lighter = max(l1, l2)
        darker = min(l1, l2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    def apply_high_contrast_theme(self):
        """应用高对比度主题"""
        high_contrast_style = f"""
            QMainWindow {{
                background-color: #000000;
                color: #FFFFFF;
            }}
            QPushButton {{
                background-color: #FFFFFF;
                color: #000000;
                border: 2px solid #FFFFFF;
                padding: {ModernDesignSystem.get_spacing('md')}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #FFFF00;
                color: #000000;
            }}
            QPushButton:focus {{
                outline: 3px solid #FFFF00;
            }}
            QLineEdit, QTextEdit {{
                background-color: #FFFFFF;
                color: #000000;
                border: 2px solid #FFFFFF;
            }}
            QLabel {{
                color: #FFFFFF;
            }}
        """
        
        self.main_window.setStyleSheet(high_contrast_style)
    
    def navigate_to_section(self, section_name: str):
        """导航到指定部分"""
        if hasattr(self.main_window, 'switch_page'):
            self.main_window.switch_page(section_name)
            self.announce_to_screen_reader(f"已切换到{section_name}页面")
    
    def trigger_new_action(self):
        """触发新建操作"""
        if hasattr(self.main_window, 'new_chat'):
            self.main_window.new_chat()
            self.announce_to_screen_reader("已创建新对话")
    
    def trigger_save_action(self):
        """触发保存操作"""
        # 实现保存逻辑
        self.announce_to_screen_reader("正在保存...")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
        键盘快捷键：
        Ctrl+1-4: 切换功能页面
        Ctrl+N: 新建对话
        Ctrl+S: 保存
        F1: 显示帮助
        Escape: 关闭对话框
        Tab: 在控件间导航
        Enter: 激活按钮或确认
        """
        
        # 创建帮助对话框
        from PyQt6.QtWidgets import QMessageBox
        msg_box = QMessageBox(self.main_window)
        msg_box.setWindowTitle("键盘快捷键帮助")
        msg_box.setText(help_text)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()
    
    def close_current_dialog(self):
        """关闭当前对话框"""
        # 查找并关闭活动的对话框
        active_window = QApplication.instance().activeWindow()
        if active_window and active_window != self.main_window:
            active_window.close()
    
    def announce_to_screen_reader(self, message: str):
        """向屏幕阅读器发送消息"""
        # 在实际应用中，这里应该使用系统的无障碍API
        # 目前使用简单的状态栏消息
        if hasattr(self.main_window, 'statusBar'):
            self.main_window.statusBar().showMessage(message, 3000)
        
        # 记录公告
        self.screen_reader_announcements.append(message)
        
        # 限制公告历史长度
        if len(self.screen_reader_announcements) > 10:
            self.screen_reader_announcements.pop(0)


class AccessibleWidget(QWidget):
    """无障碍增强的基础控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_accessibility()
    
    def setup_accessibility(self):
        """设置无障碍功能"""
        # 设置焦点策略
        self.setFocusPolicy(Qt.FocusPolicy.TabFocus)
        
        # 启用键盘导航
        self.setTabOrder(self, self)
    
    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件处理"""
        # 处理常见的键盘导航
        if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            # 模拟点击
            if hasattr(self, 'click'):
                self.click()
            elif hasattr(self, 'clicked'):
                self.clicked.emit()
        elif event.key() == Qt.Key.Key_Space:
            # 空格键激活
            if hasattr(self, 'toggle') and hasattr(self, 'isCheckable') and self.isCheckable():
                self.toggle()
        else:
            super().keyPressEvent(event)
    
    def focusInEvent(self, event: QFocusEvent):
        """获得焦点事件"""
        super().focusInEvent(event)
        # 添加焦点视觉指示
        self.setStyleSheet(self.styleSheet() + f"""
            border: 2px solid {ModernDesignSystem.get_color('primary.main')};
        """)
    
    def focusOutEvent(self, event: QFocusEvent):
        """失去焦点事件"""
        super().focusOutEvent(event)
        # 移除焦点视觉指示
        style = self.styleSheet()
        # 简单的移除方法，实际应用中可能需要更复杂的逻辑
        if "border: 2px solid" in style:
            style = style.replace(f"border: 2px solid {ModernDesignSystem.get_color('primary.main')};", "")
            self.setStyleSheet(style)


class AccessibleButton(QPushButton, AccessibleWidget):
    """无障碍增强的按钮"""
    
    def __init__(self, text: str = "", parent=None):
        QPushButton.__init__(self, text, parent)
        AccessibleWidget.__init__(self, parent)
        
        # 设置无障碍属性
        self.setAccessibleName(text)
        self.setAccessibleDescription(f"按钮: {text}")


class AccessibleLineEdit(QLineEdit, AccessibleWidget):
    """无障碍增强的输入框"""
    
    def __init__(self, placeholder: str = "", parent=None):
        QLineEdit.__init__(self, parent)
        AccessibleWidget.__init__(self, parent)
        
        if placeholder:
            self.setPlaceholderText(placeholder)
            self.setAccessibleName(f"输入框: {placeholder}")
            self.setAccessibleDescription(placeholder)


class AccessibleLabel(QLabel):
    """无障碍增强的标签"""
    
    def __init__(self, text: str = "", parent=None):
        super().__init__(text, parent)
        
        # 设置无障碍属性
        self.setAccessibleName(text)
        self.setAccessibleDescription(f"标签: {text}")
        
        # 如果是重要信息，设置为可聚焦
        if any(keyword in text.lower() for keyword in ['错误', '警告', '成功', '重要']):
            self.setFocusPolicy(Qt.FocusPolicy.TabFocus)


class KeyboardNavigationHelper:
    """键盘导航辅助类"""
    
    @staticmethod
    def setup_tab_order(widgets: List[QWidget]):
        """设置Tab键导航顺序"""
        for i in range(len(widgets) - 1):
            QWidget.setTabOrder(widgets[i], widgets[i + 1])
    
    @staticmethod
    def create_skip_link(parent: QWidget, target_widget: QWidget, text: str = "跳转到主内容") -> QPushButton:
        """创建跳转链接（用于键盘用户快速导航）"""
        skip_button = QPushButton(text, parent)
        skip_button.setStyleSheet("""
            QPushButton {
                position: absolute;
                left: -9999px;
                background: #000;
                color: #fff;
                padding: 8px;
                text-decoration: none;
                z-index: 1000;
            }
            QPushButton:focus {
                left: 6px;
                top: 7px;
            }
        """)
        
        skip_button.clicked.connect(lambda: target_widget.setFocus())
        return skip_button
    
    @staticmethod
    def announce_live_region(widget: QWidget, message: str):
        """创建实时区域公告（用于屏幕阅读器）"""
        # 创建隐藏的标签用于屏幕阅读器公告
        announcement_label = QLabel(message, widget)
        announcement_label.setAccessibleName("实时公告")
        announcement_label.setAccessibleDescription(message)
        announcement_label.hide()
        
        # 短暂显示后隐藏
        QTimer.singleShot(100, announcement_label.show)
        QTimer.singleShot(200, announcement_label.hide)
        QTimer.singleShot(3000, announcement_label.deleteLater)
